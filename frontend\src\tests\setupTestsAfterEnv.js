// Setup tests after environment is loaded
// This file runs after the test framework is installed

// Add custom matchers
require('@testing-library/jest-dom');
// Note: jest-axe/extend-expect is not available, using jest-axe directly when needed

// Extend expect with custom matchers
expect.extend({
  toBeWithinRange(received, floor, ceiling) {
    const pass = received >= floor && received <= ceiling;
    if (pass) {
      return {
        message: () =>
          `expected ${received} not to be within range ${floor} - ${ceiling}`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `expected ${received} to be within range ${floor} - ${ceiling}`,
        pass: false,
      };
    }
  },

  // Custom matcher for WebSocket messages
  toBeWebSocketMessage(received, expectedType) {
    try {
      const parsed = typeof received === 'string' ? JSON.parse(received) : received;
      const pass = parsed.type === expectedType;

      if (pass) {
        return {
          message: () => `expected message not to have type "${expectedType}"`,
          pass: true,
        };
      } else {
        return {
          message: () => `expected message to have type "${expectedType}" but got "${parsed.type}"`,
          pass: false,
        };
      }
    } catch (error) {
      return {
        message: () => `expected a valid WebSocket message but got error: ${error.message}`,
        pass: false,
      };
    }
  },
});

// Global test timeouts
jest.setTimeout(10000);

// Suppress console errors during tests
const originalConsoleError = console.error;
console.error = (...args) => {
  if (
    /Warning.*not wrapped in act/i.test(args[0]) ||
    /Warning.*ReactDOM.render is no longer supported/i.test(args[0]) ||
    /Warning.*React.createFactory/i.test(args[0])
  ) {
    return;
  }
  originalConsoleError(...args);
};

// Suppress console warnings during tests
const originalConsoleWarn = console.warn;
console.warn = (...args) => {
  if (
    /Warning.*componentWillReceiveProps has been renamed/i.test(args[0]) ||
    /Warning.*componentWillMount has been renamed/i.test(args[0])
  ) {
    return;
  }
  originalConsoleWarn(...args);
};

// Enhanced mocks for comprehensive testing
import { configure } from '@testing-library/react';

// Configure testing library
configure({ testIdAttribute: 'data-testid' });

// Mock axios for API calls
jest.mock('axios', () => ({
  default: {
    create: jest.fn(() => ({
      get: jest.fn(() => Promise.resolve({ data: {} })),
      post: jest.fn(() => Promise.resolve({ data: {} })),
      put: jest.fn(() => Promise.resolve({ data: {} })),
      patch: jest.fn(() => Promise.resolve({ data: {} })),
      delete: jest.fn(() => Promise.resolve({ data: {} })),
      interceptors: {
        request: {
          use: jest.fn(),
          eject: jest.fn(),
        },
        response: {
          use: jest.fn(),
          eject: jest.fn(),
        },
      },
      defaults: {
        headers: {
          common: {},
        },
      },
    })),
    get: jest.fn(() => Promise.resolve({ data: {} })),
    post: jest.fn(() => Promise.resolve({ data: {} })),
    put: jest.fn(() => Promise.resolve({ data: {} })),
    patch: jest.fn(() => Promise.resolve({ data: {} })),
    delete: jest.fn(() => Promise.resolve({ data: {} })),
  },
}));

// Mock useKeyboardShortcuts hook
jest.mock('../hooks/useKeyboardShortcuts', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    registerShortcut: jest.fn(),
    unregisterShortcut: jest.fn(),
    clearShortcuts: jest.fn(),
  })),
}));

// Mock WebSocket with more realistic behavior
class MockWebSocket {
  constructor(url) {
    this.url = url;
    this.readyState = WebSocket.CONNECTING;
    this.onopen = null;
    this.onclose = null;
    this.onmessage = null;
    this.onerror = null;

    // Simulate connection after a short delay
    setTimeout(() => {
      this.readyState = WebSocket.OPEN;
      if (this.onopen) this.onopen();
    }, 10);
  }

  send(data) {
    if (this.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket is not open');
    }
    // Mock echo for testing
    setTimeout(() => {
      if (this.onmessage) {
        this.onmessage({ data: `echo: ${data}` });
      }
    }, 5);
  }

  close() {
    this.readyState = WebSocket.CLOSED;
    if (this.onclose) this.onclose();
  }

  addEventListener(event, handler) {
    this[`on${event}`] = handler;
  }

  removeEventListener(event, handler) {
    this[`on${event}`] = null;
  }
}

// WebSocket constants
MockWebSocket.CONNECTING = 0;
MockWebSocket.OPEN = 1;
MockWebSocket.CLOSING = 2;
MockWebSocket.CLOSED = 3;

global.WebSocket = MockWebSocket;

// Enhanced fetch mock
global.fetch = jest.fn((url, options) => {
  const defaultResponse = {
    ok: true,
    status: 200,
    statusText: 'OK',
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
    blob: () => Promise.resolve(new Blob()),
    headers: new Map(),
  };

  if (url.includes('/api/test-error')) {
    return Promise.resolve({
      ...defaultResponse,
      ok: false,
      status: 500,
      statusText: 'Internal Server Error',
    });
  }

  return Promise.resolve(defaultResponse);
});

// Mock storage with event simulation
const createStorageMock = (name) => {
  let store = {};
  return {
    getItem: jest.fn((key) => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
      window.dispatchEvent(new StorageEvent('storage', {
        key,
        newValue: value,
        storageArea: global[name]
      }));
    }),
    removeItem: jest.fn((key) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
    get length() {
      return Object.keys(store).length;
    },
    key: jest.fn((index) => Object.keys(store)[index] || null),
  };
};

Object.defineProperty(global, 'localStorage', {
  value: createStorageMock('localStorage'),
  writable: true
});
Object.defineProperty(global, 'sessionStorage', {
  value: createStorageMock('sessionStorage'),
  writable: true
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => {
    const matches = {
      '(prefers-reduced-motion: reduce)': false,
      '(prefers-color-scheme: dark)': false,
      '(max-width: 768px)': false,
      '(min-width: 769px)': true,
    };

    return {
      matches: matches[query] || false,
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    };
  }),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation((callback) => ({
  observe: jest.fn((element) => {
    setTimeout(() => {
      callback([{
        target: element,
        contentRect: { width: 1024, height: 768 }
      }]);
    }, 10);
  }),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation((callback) => ({
  observe: jest.fn((element) => {
    setTimeout(() => {
      callback([{
        target: element,
        isIntersecting: true,
        intersectionRatio: 1,
      }]);
    }, 10);
  }),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock animation APIs
global.requestAnimationFrame = jest.fn((cb) => setTimeout(cb, 16));
global.cancelAnimationFrame = jest.fn((id) => clearTimeout(id));

// Mock performance API
global.performance = {
  ...global.performance,
  mark: jest.fn(),
  measure: jest.fn(),
  getEntriesByName: jest.fn(() => []),
  getEntriesByType: jest.fn(() => []),
  now: jest.fn(() => Date.now()),
};

// Mock URL APIs
global.URL.createObjectURL = jest.fn(() => 'mock-object-url');
global.URL.revokeObjectURL = jest.fn();

// Enhanced DOM mocks for Quill editor support
// Mock Range API for Quill editor
class MockRange {
  constructor() {
    this.startContainer = null;
    this.endContainer = null;
    this.startOffset = 0;
    this.endOffset = 0;
    this.collapsed = true;
    this.commonAncestorContainer = null;
  }

  getBoundingClientRect() {
    return {
      x: 0,
      y: 0,
      width: 100,
      height: 20,
      top: 0,
      right: 100,
      bottom: 20,
      left: 0,
      toJSON: () => ({})
    };
  }

  getClientRects() {
    return [this.getBoundingClientRect()];
  }

  setStart(node, offset) {
    this.startContainer = node;
    this.startOffset = offset;
    this.collapsed = this.startContainer === this.endContainer && this.startOffset === this.endOffset;
  }

  setEnd(node, offset) {
    this.endContainer = node;
    this.endOffset = offset;
    this.collapsed = this.startContainer === this.endContainer && this.startOffset === this.endOffset;
  }

  selectNode(node) {
    this.startContainer = node.parentNode;
    this.endContainer = node.parentNode;
    this.startOffset = Array.from(node.parentNode.childNodes).indexOf(node);
    this.endOffset = this.startOffset + 1;
    this.collapsed = false;
  }

  selectNodeContents(node) {
    this.startContainer = node;
    this.endContainer = node;
    this.startOffset = 0;
    this.endOffset = node.childNodes ? node.childNodes.length : 0;
    this.collapsed = this.startOffset === this.endOffset;
  }

  collapse(toStart = false) {
    if (toStart) {
      this.endContainer = this.startContainer;
      this.endOffset = this.startOffset;
    } else {
      this.startContainer = this.endContainer;
      this.startOffset = this.endOffset;
    }
    this.collapsed = true;
  }

  cloneRange() {
    const range = new MockRange();
    range.startContainer = this.startContainer;
    range.endContainer = this.endContainer;
    range.startOffset = this.startOffset;
    range.endOffset = this.endOffset;
    range.collapsed = this.collapsed;
    range.commonAncestorContainer = this.commonAncestorContainer;
    return range;
  }

  toString() {
    return '';
  }
}

global.Range = MockRange;

// Mock document.createRange
if (typeof document !== 'undefined') {
  document.createRange = jest.fn(() => new MockRange());
}

// Mock Selection API for Quill editor
class MockSelection {
  constructor() {
    this.anchorNode = null;
    this.anchorOffset = 0;
    this.focusNode = null;
    this.focusOffset = 0;
    this.isCollapsed = true;
    this.rangeCount = 0;
    this.type = 'None';
  }

  getRangeAt(index) {
    if (index >= this.rangeCount) {
      throw new Error('Index out of range');
    }
    return new MockRange();
  }

  addRange(range) {
    this.rangeCount = 1;
    this.anchorNode = range.startContainer;
    this.anchorOffset = range.startOffset;
    this.focusNode = range.endContainer;
    this.focusOffset = range.endOffset;
    this.isCollapsed = range.collapsed;
    this.type = range.collapsed ? 'Caret' : 'Range';
  }

  removeAllRanges() {
    this.rangeCount = 0;
    this.anchorNode = null;
    this.anchorOffset = 0;
    this.focusNode = null;
    this.focusOffset = 0;
    this.isCollapsed = true;
    this.type = 'None';
  }

  removeRange(range) {
    this.removeAllRanges();
  }

  selectAllChildren(node) {
    const range = new MockRange();
    range.selectNodeContents(node);
    this.removeAllRanges();
    this.addRange(range);
  }

  collapse(node, offset) {
    const range = new MockRange();
    range.setStart(node, offset);
    range.setEnd(node, offset);
    this.removeAllRanges();
    this.addRange(range);
  }

  toString() {
    return '';
  }
}

// Mock window.getSelection
if (typeof window !== 'undefined') {
  window.getSelection = jest.fn(() => new MockSelection());
}

// Mock document.getSelection
if (typeof document !== 'undefined') {
  document.getSelection = jest.fn(() => new MockSelection());
}

// Enhanced Element prototype mocks for Quill editor
if (typeof Element !== 'undefined') {
  // Mock getBoundingClientRect for all elements
  Element.prototype.getBoundingClientRect = jest.fn(() => ({
    x: 0,
    y: 0,
    width: 100,
    height: 20,
    top: 0,
    right: 100,
    bottom: 20,
    left: 0,
    toJSON: () => ({})
  }));

  // Mock getClientRects
  Element.prototype.getClientRects = jest.fn(() => [
    {
      x: 0,
      y: 0,
      width: 100,
      height: 20,
      top: 0,
      right: 100,
      bottom: 20,
      left: 0,
      toJSON: () => ({})
    }
  ]);

  // Mock scrollIntoView
  Element.prototype.scrollIntoView = jest.fn();

  // Mock focus and blur
  Element.prototype.focus = jest.fn();
  Element.prototype.blur = jest.fn();

  // Mock contentEditable properties
  Object.defineProperty(Element.prototype, 'contentEditable', {
    get: function () { return this._contentEditable || 'inherit'; },
    set: function (value) { this._contentEditable = value; },
    configurable: true
  });

  Object.defineProperty(Element.prototype, 'isContentEditable', {
    get: function () { return this.contentEditable === 'true'; },
    configurable: true
  });

  // Mock innerHTML and textContent setters to trigger events
  const originalInnerHTMLDescriptor = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');
  if (originalInnerHTMLDescriptor) {
    Object.defineProperty(Element.prototype, 'innerHTML', {
      get: originalInnerHTMLDescriptor.get,
      set: function (value) {
        originalInnerHTMLDescriptor.set.call(this, value);
        // Trigger input event for Quill
        this.dispatchEvent(new Event('input', { bubbles: true }));
      },
      configurable: true
    });
  }
}

// Mock window.getComputedStyle with realistic values for Quill
const originalGetComputedStyle = window.getComputedStyle;
window.getComputedStyle = jest.fn((element, pseudoElement) => {
  const mockStyle = {
    fontFamily: '"Helvetica Neue", Helvetica, Arial, sans-serif',
    fontSize: '14px',
    lineHeight: '1.42',
    color: 'rgb(0, 0, 0)',
    backgroundColor: 'rgb(255, 255, 255)',
    border: '1px solid rgb(204, 204, 204)',
    borderRadius: '4px',
    padding: '12px 15px',
    margin: '0px',
    display: 'block',
    position: 'static',
    width: '100px',
    height: '20px',
    boxSizing: 'border-box',
    getPropertyValue: jest.fn((prop) => mockStyle[prop] || ''),
    getPropertyPriority: jest.fn(() => ''),
    setProperty: jest.fn(),
    removeProperty: jest.fn(),
    item: jest.fn((index) => Object.keys(mockStyle)[index] || ''),
    length: Object.keys(mockStyle).length
  };

  // Add all CSS properties as enumerable
  Object.keys(mockStyle).forEach(key => {
    if (typeof mockStyle[key] === 'string') {
      Object.defineProperty(mockStyle, key, {
        value: mockStyle[key],
        enumerable: true,
        configurable: true
      });
    }
  });

  return mockStyle;
});

// Mock execCommand for Quill editor
if (typeof document !== 'undefined') {
  document.execCommand = jest.fn((command, showUI, value) => {
    // Mock successful execution
    return true;
  });

  document.queryCommandState = jest.fn((command) => {
    // Mock command state
    return false;
  });

  document.queryCommandSupported = jest.fn((command) => {
    // Mock command support
    return true;
  });

  document.queryCommandValue = jest.fn((command) => {
    // Mock command value
    return '';
  });
}

// Mock File APIs
global.File = class MockFile {
  constructor(bits, name, options = {}) {
    this.bits = bits;
    this.name = name;
    this.size = bits.reduce((acc, bit) => acc + bit.length, 0);
    this.type = options.type || '';
    this.lastModified = options.lastModified || Date.now();
  }
};

global.FileReader = class MockFileReader {
  constructor() {
    this.readyState = 0;
    this.result = null;
    this.error = null;
  }

  readAsText(file) {
    setTimeout(() => {
      this.readyState = 2;
      this.result = 'mock file content';
      if (this.onload) this.onload();
    }, 10);
  }

  readAsDataURL(file) {
    setTimeout(() => {
      this.readyState = 2;
      this.result = 'data:text/plain;base64,bW9jayBmaWxlIGNvbnRlbnQ=';
      if (this.onload) this.onload();
    }, 10);
  }
};

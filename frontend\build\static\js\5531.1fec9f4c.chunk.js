"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[5531],{

/***/ 57683:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var _utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(16918);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(70572);
/* harmony import */ var _contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(82569);


var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8;

// Optimized Ant Design imports for better tree-shaking





// Animations
var rotate = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* .keyframes */ .i7)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n"])));
var fadeIn = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* .keyframes */ .i7)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  from {\n    opacity: 0;\n    transform: scale(0.8);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n"])));

// Styled components
var ToggleContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"])));
var ToggleButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay)(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  border: 1px solid var(--color-border);\n  background-color: var(--color-surface);\n  color: var(--color-text);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n\n  /* Ensure WCAG AA contrast compliance */\n  &:hover, &:focus {\n    border-color: var(--color-primary);\n    color: var(--color-primary);\n    background-color: var(--color-background-secondary);\n    transform: scale(1.05);\n    box-shadow: var(--shadow-md);\n  }\n\n  &:focus-visible {\n    outline: 2px solid var(--color-primary);\n    outline-offset: 2px;\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n\n  .anticon {\n    font-size: 18px;\n    animation: ", " 0.3s ease;\n\n    /* Ensure icon has sufficient contrast */\n    filter: contrast(1.1);\n  }\n\n  &.rotating .anticon {\n    animation: ", " 0.5s ease;\n  }\n\n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    border-width: 2px;\n    font-weight: 600;\n\n    &:hover, &:focus {\n      border-width: 3px;\n    }\n\n    .anticon {\n      filter: contrast(1.3);\n    }\n  }\n\n  /* Reduced motion support */\n  @media (prefers-reduced-motion: reduce) {\n    transition: none;\n\n    &:hover {\n      transform: none;\n    }\n\n    &:active {\n      transform: none;\n    }\n\n    .anticon {\n      animation: none;\n    }\n\n    &.rotating .anticon {\n      animation: none;\n    }\n  }\n"])), fadeIn, rotate);
var DropdownContent = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  min-width: 180px;\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: 8px;\n  box-shadow: var(--shadow-lg);\n  padding: 4px;\n"])));
var ThemeOption = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 12px 16px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border-radius: 6px;\n  margin: 2px 0;\n  color: var(--color-text);\n\n  &:hover {\n    background-color: var(--color-background-secondary);\n    transform: translateX(2px);\n  }\n\n  &:focus {\n    outline: 2px solid var(--color-primary);\n    outline-offset: 1px;\n  }\n\n  &.active {\n    background-color: var(--color-primary);\n    color: white;\n    box-shadow: var(--shadow-sm);\n  }\n\n  .option-content {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n  }\n\n  .anticon {\n    font-size: 16px;\n\n    /* Ensure icon contrast in active state */\n    filter: ", ";\n  }\n\n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    border: 1px solid var(--color-border);\n\n    &:hover {\n      border-color: var(--color-primary);\n    }\n\n    &.active {\n      border: 2px solid white;\n    }\n  }\n\n  /* Reduced motion support */\n  @media (prefers-reduced-motion: reduce) {\n    transition: background-color 0.2s ease;\n\n    &:hover {\n      transform: none;\n    }\n  }\n"])), function (props) {
  var _props$className;
  return (_props$className = props.className) !== null && _props$className !== void 0 && _props$className.includes('active') ? 'none' : 'contrast(1.1)';
});
var ThemeLabel = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.span(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  font-size: 14px;\n  font-weight: 500;\n  line-height: 1.2;\n"])));
var ThemeDescription = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  font-size: 12px;\n  color: ", ";\n  margin-top: 2px;\n  line-height: 1.2;\n\n  /* Ensure description text meets contrast requirements */\n  opacity: ", ";\n"])), function (props) {
  return props.active ? 'rgba(255, 255, 255, 0.8)' : 'var(--color-text-secondary)';
}, function (props) {
  return props.active ? 0.9 : 0.8;
});
var DarkModeToggle = function DarkModeToggle(_ref) {
  var _ref$showDropdown = _ref.showDropdown,
    showDropdown = _ref$showDropdown === void 0 ? true : _ref$showDropdown,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'default' : _ref$size;
  var _useEnhancedTheme = (0,_contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_6__/* .useEnhancedTheme */ .ZV)(),
    isDarkMode = _useEnhancedTheme.isDarkMode,
    themeMode = _useEnhancedTheme.themeMode,
    toggleDarkMode = _useEnhancedTheme.toggleDarkMode,
    setThemeMode = _useEnhancedTheme.setThemeMode,
    systemPrefersDark = _useEnhancedTheme.systemPrefersDark;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    isRotating = _useState2[0],
    setIsRotating = _useState2[1];
  var handleToggle = function handleToggle() {
    setIsRotating(true);
    toggleDarkMode();
    setTimeout(function () {
      return setIsRotating(false);
    }, 500);
  };
  var handleThemeChange = function handleThemeChange(mode) {
    setIsRotating(true);
    setThemeMode(mode);
    setTimeout(function () {
      return setIsRotating(false);
    }, 500);
  };
  var getIcon = function getIcon() {
    if (themeMode === 'system') {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DesktopOutlined */ .zlw, null);
    }
    return isDarkMode ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .MoonOutlined */ .IO1, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .SunOutlined */ .qVE, null);
  };
  var getTooltipTitle = function getTooltipTitle() {
    switch (themeMode) {
      case 'light':
        return 'Light mode';
      case 'dark':
        return 'Dark mode';
      case 'system':
        return "System mode (".concat(systemPrefersDark ? 'dark' : 'light', ")");
      default:
        return 'Toggle theme';
    }
  };
  var themeOptions = [{
    key: 'light',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .SunOutlined */ .qVE, null),
    label: 'Light',
    description: 'Light theme'
  }, {
    key: 'dark',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .MoonOutlined */ .IO1, null),
    label: 'Dark',
    description: 'Dark theme'
  }, {
    key: 'system',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DesktopOutlined */ .zlw, null),
    label: 'System',
    description: 'Follow system preference'
  }];
  var dropdownMenu = {
    items: themeOptions.map(function (option) {
      return {
        key: option.key,
        label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ThemeOption, {
          className: themeMode === option.key ? 'active' : '',
          onClick: function onClick() {
            return handleThemeChange(option.key);
          },
          role: "menuitem",
          tabIndex: 0,
          "aria-selected": themeMode === option.key,
          onKeyDown: function onKeyDown(e) {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleThemeChange(option.key);
            }
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
          className: "option-content"
        }, option.icon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ThemeLabel, null, option.label), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ThemeDescription, {
          active: themeMode === option.key
        }, option.description))), themeMode === option.key && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .CheckOutlined */ .JIb, {
          "aria-label": "Selected"
        }))
      };
    })
  };
  if (!showDropdown) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ToggleContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
      title: getTooltipTitle(),
      placement: "bottom"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ToggleButton, {
      type: "text",
      size: size,
      className: isRotating ? 'rotating' : '',
      onClick: handleToggle,
      "aria-label": "Switch to ".concat(isDarkMode ? 'light' : 'dark', " mode. Current theme: ").concat(getTooltipTitle()),
      "aria-pressed": isDarkMode,
      role: "switch"
    }, getIcon())));
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ToggleContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Dropdown */ .ms, {
    menu: dropdownMenu,
    trigger: ['click'],
    placement: "bottomRight",
    arrow: true,
    dropdownRender: function dropdownRender(menu) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(DropdownContent, {
        role: "menu",
        "aria-label": "Theme selection menu"
      }, menu);
    },
    onOpenChange: function onOpenChange(open) {
      // Announce to screen readers when menu opens/closes
      if (open) {
        // Focus management for accessibility
        setTimeout(function () {
          var firstMenuItem = document.querySelector('[role="menuitem"]');
          if (firstMenuItem) {
            firstMenuItem.focus();
          }
        }, 100);
      }
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
    title: getTooltipTitle(),
    placement: "bottom"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ToggleButton, {
    type: "text",
    size: size,
    className: isRotating ? 'rotating' : '',
    "aria-label": "Theme options menu. Current theme: ".concat(getTooltipTitle()),
    "aria-haspopup": "menu",
    "aria-expanded": "false",
    role: "button"
  }, getIcon()))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DarkModeToggle);

/***/ }),

/***/ 75531:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ test_ContrastTest)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./src/contexts/EnhancedThemeContext.js
var EnhancedThemeContext = __webpack_require__(82569);
// EXTERNAL MODULE: ./src/components/ui/DarkModeToggle.js
var DarkModeToggle = __webpack_require__(57683);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./src/design-system/index.js + 7 modules
var design_system = __webpack_require__(79146);
;// ./src/utils/accessibility.js



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Accessibility Utilities
 * 
 * Comprehensive accessibility utilities for WCAG 2.1 AA compliance
 * including keyboard navigation, screen reader support, focus management,
 * and accessible drag-and-drop interactions.
 */



/**
 * ARIA utilities for screen reader support
 */
var ariaUtils = {
  /**
   * Generate unique IDs for ARIA relationships
   */
  generateId: function generateId() {
    var prefix = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'aria';
    return "".concat(prefix, "-").concat(Math.random().toString(36).substr(2, 9));
  },
  /**
   * Create ARIA label for components
   */
  createLabel: function createLabel(component) {
    var _component$props, _component$props2, _component$props3;
    var action = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'interact with';
    var type = component.type || 'component';
    var label = ((_component$props = component.props) === null || _component$props === void 0 ? void 0 : _component$props.label) || ((_component$props2 = component.props) === null || _component$props2 === void 0 ? void 0 : _component$props2.title) || ((_component$props3 = component.props) === null || _component$props3 === void 0 ? void 0 : _component$props3.text) || type;
    return "".concat(action, " ").concat(label, " ").concat(type);
  },
  /**
   * Create ARIA description for components
   */
  createDescription: function createDescription(component) {
    var _component$props4, _component$props5, _component$props6, _component$props7;
    var descriptions = [];
    if ((_component$props4 = component.props) !== null && _component$props4 !== void 0 && _component$props4.description) {
      descriptions.push(component.props.description);
    }
    if ((_component$props5 = component.props) !== null && _component$props5 !== void 0 && _component$props5.placeholder) {
      descriptions.push("Placeholder: ".concat(component.props.placeholder));
    }
    if ((_component$props6 = component.props) !== null && _component$props6 !== void 0 && _component$props6.required) {
      descriptions.push('Required field');
    }
    if ((_component$props7 = component.props) !== null && _component$props7 !== void 0 && _component$props7.disabled) {
      descriptions.push('Disabled');
    }
    return descriptions.join('. ');
  },
  /**
   * Create live region announcements
   */
  announce: function announce(message) {
    var priority = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'polite';
    var announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);

    // Remove after announcement
    setTimeout(function () {
      document.body.removeChild(announcement);
    }, 1000);
  },
  /**
   * Create ARIA attributes for drag and drop
   */
  createDragDropAttributes: function createDragDropAttributes(component) {
    var isDragging = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
    var isDropTarget = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
    var attributes = {
      'aria-grabbed': isDragging,
      'aria-describedby': "".concat(component.id, "-drag-instructions")
    };
    if (isDropTarget) {
      attributes['aria-dropeffect'] = 'move';
    }
    return attributes;
  },
  /**
   * Create skip links for keyboard navigation
   */
  createSkipLink: function createSkipLink(targetId, text) {
    return {
      href: "#".concat(targetId),
      className: 'skip-link',
      'aria-label': text,
      onKeyDown: function onKeyDown(e) {
        if (e.key === 'Enter') {
          var _document$getElementB;
          e.preventDefault();
          (_document$getElementB = document.getElementById(targetId)) === null || _document$getElementB === void 0 || _document$getElementB.focus();
        }
      }
    };
  }
};

/**
 * Keyboard navigation utilities
 */
var keyboardUtils = {
  /**
   * Standard key codes for accessibility
   */
  KEYS: {
    ENTER: 'Enter',
    SPACE: ' ',
    ESCAPE: 'Escape',
    TAB: 'Tab',
    ARROW_UP: 'ArrowUp',
    ARROW_DOWN: 'ArrowDown',
    ARROW_LEFT: 'ArrowLeft',
    ARROW_RIGHT: 'ArrowRight',
    HOME: 'Home',
    END: 'End',
    PAGE_UP: 'PageUp',
    PAGE_DOWN: 'PageDown',
    DELETE: 'Delete',
    BACKSPACE: 'Backspace'
  },
  /**
   * Handle keyboard activation (Enter/Space)
   */
  handleActivation: function handleActivation(callback) {
    return function (e) {
      if (e.key === keyboardUtils.KEYS.ENTER || e.key === keyboardUtils.KEYS.SPACE) {
        e.preventDefault();
        callback(e);
      }
    };
  },
  /**
   * Handle arrow key navigation in lists/grids
   */
  handleArrowNavigation: function handleArrowNavigation(elements, currentIndex) {
    var orientation = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'vertical';
    return function (e) {
      var newIndex = currentIndex;
      switch (e.key) {
        case keyboardUtils.KEYS.ARROW_UP:
          if (orientation === 'vertical') {
            newIndex = Math.max(0, currentIndex - 1);
            e.preventDefault();
          }
          break;
        case keyboardUtils.KEYS.ARROW_DOWN:
          if (orientation === 'vertical') {
            newIndex = Math.min(elements.length - 1, currentIndex + 1);
            e.preventDefault();
          }
          break;
        case keyboardUtils.KEYS.ARROW_LEFT:
          if (orientation === 'horizontal') {
            newIndex = Math.max(0, currentIndex - 1);
            e.preventDefault();
          }
          break;
        case keyboardUtils.KEYS.ARROW_RIGHT:
          if (orientation === 'horizontal') {
            newIndex = Math.min(elements.length - 1, currentIndex + 1);
            e.preventDefault();
          }
          break;
        case keyboardUtils.KEYS.HOME:
          newIndex = 0;
          e.preventDefault();
          break;
        case keyboardUtils.KEYS.END:
          newIndex = elements.length - 1;
          e.preventDefault();
          break;
      }
      if (newIndex !== currentIndex && elements[newIndex]) {
        elements[newIndex].focus();
        return newIndex;
      }
      return currentIndex;
    };
  },
  /**
   * Create roving tabindex for component groups
   */
  createRovingTabindex: function createRovingTabindex(elements) {
    var activeIndex = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
    return elements.map(function (element, index) {
      return _objectSpread(_objectSpread({}, element), {}, {
        tabIndex: index === activeIndex ? 0 : -1,
        'aria-selected': index === activeIndex
      });
    });
  },
  /**
   * Handle escape key to close modals/dropdowns
   */
  handleEscape: function handleEscape(callback) {
    return function (e) {
      if (e.key === keyboardUtils.KEYS.ESCAPE) {
        e.preventDefault();
        callback(e);
      }
    };
  }
};

/**
 * Focus management utilities
 */
var focusUtils = {
  /**
   * Focus trap for modals and dialogs
   */
  createFocusTrap: function createFocusTrap(containerRef) {
    var getFocusableElements = function getFocusableElements() {
      if (!containerRef.current) return [];
      var focusableSelectors = ['button:not([disabled])', 'input:not([disabled])', 'select:not([disabled])', 'textarea:not([disabled])', 'a[href]', '[tabindex]:not([tabindex="-1"])', '[contenteditable="true"]'].join(', ');
      return Array.from(containerRef.current.querySelectorAll(focusableSelectors));
    };
    var handleKeyDown = function handleKeyDown(e) {
      if (e.key !== keyboardUtils.KEYS.TAB) return;
      var focusableElements = getFocusableElements();
      if (focusableElements.length === 0) return;
      var firstElement = focusableElements[0];
      var lastElement = focusableElements[focusableElements.length - 1];
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    };
    var activate = function activate() {
      var focusableElements = getFocusableElements();
      if (focusableElements.length > 0) {
        focusableElements[0].focus();
      }
      document.addEventListener('keydown', handleKeyDown);
    };
    var deactivate = function deactivate() {
      document.removeEventListener('keydown', handleKeyDown);
    };
    return {
      activate: activate,
      deactivate: deactivate
    };
  },
  /**
   * Restore focus to previous element
   */
  createFocusRestore: function createFocusRestore() {
    var previousActiveElement = document.activeElement;
    return function () {
      if (previousActiveElement && typeof previousActiveElement.focus === 'function') {
        previousActiveElement.focus();
      }
    };
  },
  /**
   * Focus first error in form validation
   */
  focusFirstError: function focusFirstError(formRef) {
    var errorClass = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '.ant-form-item-has-error';
    if (!formRef.current) return;
    var firstError = formRef.current.querySelector("".concat(errorClass, " input, ").concat(errorClass, " textarea, ").concat(errorClass, " select"));
    if (firstError) {
      firstError.focus();
      firstError.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  },
  /**
   * Manage focus for dynamic content
   */
  manageDynamicFocus: function manageDynamicFocus(newContentRef, announcement) {
    if (newContentRef.current) {
      // Focus the new content
      newContentRef.current.focus();

      // Announce the change
      if (announcement) {
        ariaUtils.announce(announcement);
      }
    }
  }
};

/**
 * Color contrast utilities for WCAG compliance
 */
var contrastUtils = {
  /**
   * Calculate relative luminance
   */
  getLuminance: function getLuminance(color) {
    var rgb = contrastUtils.hexToRgb(color);
    if (!rgb) return 0;
    var _map = [rgb.r, rgb.g, rgb.b].map(function (c) {
        c = c / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      }),
      _map2 = (0,slicedToArray/* default */.A)(_map, 3),
      r = _map2[0],
      g = _map2[1],
      b = _map2[2];
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  },
  /**
   * Convert hex to RGB
   */
  hexToRgb: function hexToRgb(hex) {
    var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  },
  /**
   * Calculate contrast ratio between two colors
   */
  getContrastRatio: function getContrastRatio(color1, color2) {
    var lum1 = contrastUtils.getLuminance(color1);
    var lum2 = contrastUtils.getLuminance(color2);
    var brightest = Math.max(lum1, lum2);
    var darkest = Math.min(lum1, lum2);
    return (brightest + 0.05) / (darkest + 0.05);
  },
  /**
   * Check if color combination meets WCAG standards
   */
  meetsWCAG: function meetsWCAG(foreground, background) {
    var level = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'AA';
    var size = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'normal';
    var ratio = contrastUtils.getContrastRatio(foreground, background);
    if (level === 'AAA') {
      return size === 'large' ? ratio >= 4.5 : ratio >= 7;
    }
    return size === 'large' ? ratio >= 3 : ratio >= 4.5;
  },
  /**
   * Get accessible text color for background
   */
  getAccessibleTextColor: function getAccessibleTextColor(backgroundColor) {
    var whiteContrast = contrastUtils.getContrastRatio('#FFFFFF', backgroundColor);
    var blackContrast = contrastUtils.getContrastRatio('#000000', backgroundColor);
    return whiteContrast > blackContrast ? '#FFFFFF' : '#000000';
  }
};

/**
 * Accessible drag and drop utilities
 */
var accessibleDragDrop = {
  /**
   * Create keyboard-accessible drag and drop
   */
  createKeyboardDragDrop: function createKeyboardDragDrop(items, onMove) {
    var draggedIndex = null;
    var dropTargetIndex = null;
    var handleKeyDown = function handleKeyDown(e, index) {
      switch (e.key) {
        case keyboardUtils.KEYS.SPACE:
          e.preventDefault();
          if (draggedIndex === null) {
            // Start drag
            draggedIndex = index;
            ariaUtils.announce("Picked up item ".concat(index + 1, ". Use arrow keys to move, space to drop, escape to cancel."));
          } else if (draggedIndex === index) {
            // Drop at current position
            if (dropTargetIndex !== null && dropTargetIndex !== draggedIndex) {
              onMove(draggedIndex, dropTargetIndex);
              ariaUtils.announce("Moved item from position ".concat(draggedIndex + 1, " to position ").concat(dropTargetIndex + 1, "."));
            }
            draggedIndex = null;
            dropTargetIndex = null;
          }
          break;
        case keyboardUtils.KEYS.ESCAPE:
          if (draggedIndex !== null) {
            e.preventDefault();
            draggedIndex = null;
            dropTargetIndex = null;
            ariaUtils.announce('Drag operation cancelled.');
          }
          break;
        case keyboardUtils.KEYS.ARROW_UP:
        case keyboardUtils.KEYS.ARROW_DOWN:
          if (draggedIndex !== null) {
            e.preventDefault();
            var direction = e.key === keyboardUtils.KEYS.ARROW_UP ? -1 : 1;
            var newIndex = Math.max(0, Math.min(items.length - 1, index + direction));
            if (newIndex !== index) {
              dropTargetIndex = newIndex;
              ariaUtils.announce("Moving to position ".concat(newIndex + 1, "."));
            }
          }
          break;
      }
    };
    return {
      handleKeyDown: handleKeyDown,
      getDragAttributes: function getDragAttributes(index) {
        return {
          'aria-grabbed': draggedIndex === index,
          'aria-dropeffect': draggedIndex !== null && draggedIndex !== index ? 'move' : 'none',
          'aria-describedby': "drag-instructions-".concat(index)
        };
      }
    };
  },
  /**
   * Create drag instructions for screen readers
   */
  createDragInstructions: function createDragInstructions(id) {
    return {
      id: "drag-instructions-".concat(id),
      className: 'sr-only',
      children: 'Press space to pick up this item. Use arrow keys to move it to a new position, then press space again to drop it. Press escape to cancel.'
    };
  }
};

/**
 * Screen reader utilities
 */
var screenReaderUtils = {
  /**
   * Create screen reader only content
   */
  srOnly: function srOnly(content) {
    return {
      className: 'sr-only',
      children: content,
      'aria-hidden': false
    };
  },
  /**
   * Hide decorative content from screen readers
   */
  hideFromScreenReader: function hideFromScreenReader() {
    return {
      'aria-hidden': true
    };
  },
  /**
   * Create accessible loading states
   */
  createLoadingState: function createLoadingState(isLoading) {
    var loadingText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'Loading...';
    return {
      'aria-busy': isLoading,
      'aria-live': 'polite',
      'aria-label': isLoading ? loadingText : undefined
    };
  },
  /**
   * Create accessible error states
   */
  createErrorState: function createErrorState(hasError, errorMessage) {
    return {
      'aria-invalid': hasError,
      'aria-describedby': hasError ? 'error-message' : undefined,
      role: hasError ? 'alert' : undefined
    };
  }
};

/**
 * High contrast mode utilities
 */
var highContrastUtils = {
  /**
   * Detect high contrast mode
   */
  isHighContrastMode: function isHighContrastMode() {
    return window.matchMedia('(prefers-contrast: high)').matches;
  },
  /**
   * Create high contrast styles
   */
  createHighContrastStyles: function createHighContrastStyles(baseStyles) {
    return _objectSpread(_objectSpread({}, baseStyles), {}, {
      '@media (prefers-contrast: high)': {
        border: '2px solid',
        outline: '1px solid',
        backgroundColor: 'Canvas',
        color: 'CanvasText'
      }
    });
  },
  /**
   * Ensure minimum contrast in high contrast mode
   */
  ensureHighContrast: function ensureHighContrast(element) {
    if (highContrastUtils.isHighContrastMode()) {
      element.style.border = '2px solid';
      element.style.outline = '1px solid';
    }
  }
};

/**
 * Reduced motion utilities
 */
var reducedMotionUtils = {
  /**
   * Check if user prefers reduced motion
   */
  prefersReducedMotion: function prefersReducedMotion() {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },
  /**
   * Create motion-safe animations
   */
  createMotionSafeAnimation: function createMotionSafeAnimation(animation) {
    return {
      animation: reducedMotionUtils.prefersReducedMotion() ? 'none' : animation,
      transition: reducedMotionUtils.prefersReducedMotion() ? 'none' : design_system.theme.transitions["default"]
    };
  },
  /**
   * Respect reduced motion preferences
   */
  respectReducedMotion: function respectReducedMotion(styles) {
    return _objectSpread(_objectSpread({}, styles), {}, {
      '@media (prefers-reduced-motion: reduce)': {
        animation: 'none !important',
        transition: 'none !important'
      }
    });
  }
};

/**
 * Accessibility testing utilities
 */
var a11yTestUtils = {
  /**
   * Check if element has proper ARIA attributes
   */
  validateAriaAttributes: function validateAriaAttributes(element) {
    var issues = [];

    // Check for required ARIA attributes
    if (element.getAttribute('role') === 'button' && !element.hasAttribute('aria-label') && !element.textContent.trim()) {
      issues.push('Button missing accessible name');
    }
    if (element.hasAttribute('aria-describedby')) {
      var describedById = element.getAttribute('aria-describedby');
      if (!document.getElementById(describedById)) {
        issues.push("aria-describedby references non-existent element: ".concat(describedById));
      }
    }
    if (element.hasAttribute('aria-labelledby')) {
      var labelledById = element.getAttribute('aria-labelledby');
      if (!document.getElementById(labelledById)) {
        issues.push("aria-labelledby references non-existent element: ".concat(labelledById));
      }
    }
    return issues;
  },
  /**
   * Check keyboard accessibility
   */
  validateKeyboardAccess: function validateKeyboardAccess(element) {
    var issues = [];
    var interactiveElements = ['button', 'input', 'select', 'textarea', 'a'];
    var tagName = element.tagName.toLowerCase();
    var role = element.getAttribute('role');
    if (interactiveElements.includes(tagName) || role === 'button') {
      if (element.tabIndex < 0 && !element.hasAttribute('disabled')) {
        issues.push('Interactive element not keyboard accessible');
      }
    }
    return issues;
  },
  /**
   * Check color contrast
   */
  validateColorContrast: function validateColorContrast(element) {
    var issues = [];
    var computedStyle = window.getComputedStyle(element);
    var color = computedStyle.color;
    var backgroundColor = computedStyle.backgroundColor;
    if (color && backgroundColor && color !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'rgba(0, 0, 0, 0)') {
      var ratio = contrastUtils.getContrastRatio(color, backgroundColor);
      if (ratio < 4.5) {
        issues.push("Insufficient color contrast: ".concat(ratio.toFixed(2), ":1 (minimum 4.5:1)"));
      }
    }
    return issues;
  },
  /**
   * Run comprehensive accessibility audit
   */
  auditElement: function auditElement(element) {
    var issues = [].concat((0,toConsumableArray/* default */.A)(a11yTestUtils.validateAriaAttributes(element)), (0,toConsumableArray/* default */.A)(a11yTestUtils.validateKeyboardAccess(element)), (0,toConsumableArray/* default */.A)(a11yTestUtils.validateColorContrast(element)));
    return {
      element: element,
      issues: issues,
      isAccessible: issues.length === 0
    };
  }
};

/**
 * Accessibility monitoring utilities
 */
var a11yMonitorUtils = {
  /**
   * Monitor focus changes for debugging
   */
  monitorFocus: function monitorFocus() {
    var enabled = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
    if (!enabled) return;
    var previousFocus = null;
    var handleFocusChange = function handleFocusChange(e) {
      console.log('Focus changed:', {
        from: previousFocus,
        to: e.target,
        timestamp: new Date().toISOString()
      });
      previousFocus = e.target;
    };
    document.addEventListener('focusin', handleFocusChange);
    document.addEventListener('focusout', handleFocusChange);
    return function () {
      document.removeEventListener('focusin', handleFocusChange);
      document.removeEventListener('focusout', handleFocusChange);
    };
  },
  /**
   * Monitor ARIA live region announcements
   */
  monitorLiveRegions: function monitorLiveRegions() {
    var enabled = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
    if (!enabled) return;
    var observer = new MutationObserver(function (mutations) {
      mutations.forEach(function (mutation) {
        if (mutation.type === 'childList') {
          var target = mutation.target;
          var ariaLive = target.getAttribute('aria-live');
          if (ariaLive) {
            console.log('Live region updated:', {
              element: target,
              content: target.textContent,
              priority: ariaLive,
              timestamp: new Date().toISOString()
            });
          }
        }
      });
    });
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    return function () {
      return observer.disconnect();
    };
  },
  /**
   * Log accessibility violations
   */
  logViolations: function logViolations(violations) {
    if (false) {}
  }
};

/**
 * Enhanced contrast utilities for better accessibility
 */
var enhancedContrastUtils = {
  /**
   * Apply enhanced contrast mode for better accessibility
   */
  apply: function apply() {
    var enable = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
    var body = document.body;
    if (enable) {
      body.classList.add('enhanced-contrast');
      localStorage.setItem('accessibility_enhancedContrast', 'true');
    } else {
      body.classList.remove('enhanced-contrast');
      localStorage.setItem('accessibility_enhancedContrast', 'false');
    }
  },
  /**
   * Check if enhanced contrast mode is enabled
   */
  isEnabled: function isEnabled() {
    return localStorage.getItem('accessibility_enhancedContrast') === 'true' || document.body.classList.contains('enhanced-contrast');
  },
  /**
   * Initialize enhanced contrast mode based on user preference
   */
  init: function init() {
    var isEnabled = localStorage.getItem('accessibility_enhancedContrast') === 'true';
    if (isEnabled) {
      enhancedContrastUtils.apply(true);
    }
  },
  /**
   * Toggle enhanced contrast mode
   */
  toggle: function toggle() {
    var isCurrentlyEnabled = enhancedContrastUtils.isEnabled();
    enhancedContrastUtils.apply(!isCurrentlyEnabled);
    return !isCurrentlyEnabled;
  }
};

// Export all utilities
/* harmony default export */ const accessibility = ({
  aria: ariaUtils,
  keyboard: keyboardUtils,
  focus: focusUtils,
  contrast: contrastUtils,
  dragDrop: accessibleDragDrop,
  screenReader: screenReaderUtils,
  highContrast: highContrastUtils,
  reducedMotion: reducedMotionUtils,
  testing: a11yTestUtils,
  monitoring: a11yMonitorUtils,
  enhancedContrast: enhancedContrastUtils
});
// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 10 modules
var styled_components_browser_esm = __webpack_require__(70572);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
;// ./src/components/test/ContrastTest.js


var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;







var Title = es/* Typography */.o5.Title,
  Paragraph = es/* Typography */.o5.Paragraph,
  Text = es/* Typography */.o5.Text;
var TestContainer = styled_components_browser_esm/* default */.Ay.div(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: var(--spacing-lg);\n  background-color: var(--color-background);\n  min-height: 100vh;\n  transition: all 0.3s ease;\n"])));
var TestCard = (0,styled_components_browser_esm/* default */.Ay)(es/* Card */.Zp)(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: var(--spacing-lg);\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  .ant-card-head {\n    background-color: var(--color-background-secondary);\n    border-bottom: 1px solid var(--color-border-light);\n  }\n\n  .ant-card-head-title {\n    color: var(--color-text);\n  }\n"])));
var ContrastGrid = styled_components_browser_esm/* default */.Ay.div(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: var(--spacing-md);\n  margin: var(--spacing-md) 0;\n"])));
var ContrastBox = styled_components_browser_esm/* default */.Ay.div(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: var(--spacing-md);\n  border-radius: var(--border-radius-md);\n  border: 1px solid var(--color-border);\n  text-align: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-md);\n  }\n"])));
var StatusIndicator = styled_components_browser_esm/* default */.Ay.div(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: inline-flex;\n  align-items: center;\n  gap: var(--spacing-xs);\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--border-radius-sm);\n  font-size: 12px;\n  font-weight: 500;\n  margin: var(--spacing-xs);\n"])));
var ContrastTest = function ContrastTest() {
  var _useEnhancedTheme = (0,EnhancedThemeContext/* useEnhancedTheme */.ZV)(),
    isDarkMode = _useEnhancedTheme.isDarkMode,
    colors = _useEnhancedTheme.colors,
    themeMode = _useEnhancedTheme.themeMode;
  var _useState = (0,react.useState)(enhancedContrastUtils.isEnabled()),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    enhancedContrast = _useState2[0],
    setEnhancedContrast = _useState2[1];
  var handleToggleEnhancedContrast = function handleToggleEnhancedContrast(checked) {
    setEnhancedContrast(checked);
    enhancedContrastUtils.apply(checked);
  };
  var statusVariants = [{
    type: 'success',
    color: '#52c41a',
    bg: 'rgba(82, 196, 26, 0.1)',
    text: 'Success Status'
  }, {
    type: 'warning',
    color: '#faad14',
    bg: 'rgba(250, 173, 20, 0.1)',
    text: 'Warning Status'
  }, {
    type: 'error',
    color: '#ff4d4f',
    bg: 'rgba(255, 77, 79, 0.1)',
    text: 'Error Status'
  }, {
    type: 'info',
    color: '#1890ff',
    bg: 'rgba(24, 144, 255, 0.1)',
    text: 'Info Status'
  }];
  return /*#__PURE__*/react.createElement(TestContainer, null, /*#__PURE__*/react.createElement(TestCard, {
    title: "Text Contrast & Visibility Test"
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      marginBottom: '24px',
      textAlign: 'center'
    }
  }, /*#__PURE__*/react.createElement(Title, {
    level: 3
  }, "Current Theme: ", themeMode, " mode"), /*#__PURE__*/react.createElement(es/* Space */.$x, {
    size: "large"
  }, /*#__PURE__*/react.createElement(DarkModeToggle/* default */.A, null), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, "Enhanced Contrast:"), /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    checked: enhancedContrast,
    onChange: handleToggleEnhancedContrast,
    checkedChildren: "On",
    unCheckedChildren: "Off",
    style: {
      marginLeft: '8px'
    }
  })))), /*#__PURE__*/react.createElement(es/* Divider */.cG, null, "Typography Hierarchy"), /*#__PURE__*/react.createElement(es/* Space */.$x, {
    direction: "vertical",
    size: "large",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(Title, {
    level: 1,
    style: {
      color: 'var(--color-text)'
    }
  }, "Heading Level 1"), /*#__PURE__*/react.createElement(Title, {
    level: 2,
    style: {
      color: 'var(--color-text)'
    }
  }, "Heading Level 2"), /*#__PURE__*/react.createElement(Title, {
    level: 3,
    style: {
      color: 'var(--color-text)'
    }
  }, "Heading Level 3"), /*#__PURE__*/react.createElement(Title, {
    level: 4,
    style: {
      color: 'var(--color-text)'
    }
  }, "Heading Level 4"), /*#__PURE__*/react.createElement(Title, {
    level: 5,
    style: {
      color: 'var(--color-text)'
    }
  }, "Heading Level 5")), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(Paragraph, {
    style: {
      color: 'var(--color-text)'
    }
  }, "This is a regular paragraph with normal text color. It should be easily readable against the current background in both light and dark modes."), /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, "This is secondary text that should have good contrast."), /*#__PURE__*/react.createElement("br", null), /*#__PURE__*/react.createElement(Text, {
    type: "success"
  }, "Success text should be visible and accessible."), /*#__PURE__*/react.createElement("br", null), /*#__PURE__*/react.createElement(Text, {
    type: "warning"
  }, "Warning text should stand out appropriately."), /*#__PURE__*/react.createElement("br", null), /*#__PURE__*/react.createElement(Text, {
    type: "danger"
  }, "Error text should be clearly visible."), /*#__PURE__*/react.createElement("br", null), /*#__PURE__*/react.createElement(Text, {
    disabled: true
  }, "Disabled text should be distinguishable but subdued."))), /*#__PURE__*/react.createElement(es/* Divider */.cG, null, "Status Indicators"), /*#__PURE__*/react.createElement("div", null, statusVariants.map(function (status, index) {
    return /*#__PURE__*/react.createElement(StatusIndicator, {
      key: index,
      style: {
        color: status.color,
        backgroundColor: status.bg,
        border: "1px solid ".concat(status.color)
      }
    }, status.type === 'success' && /*#__PURE__*/react.createElement(icons_es/* CheckCircleOutlined */.hWy, null), status.type === 'warning' && /*#__PURE__*/react.createElement(icons_es/* ExclamationCircleOutlined */.G2i, null), status.type === 'error' && /*#__PURE__*/react.createElement(icons_es/* CloseCircleOutlined */.bBN, null), status.type === 'info' && /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null), status.text);
  })), /*#__PURE__*/react.createElement(es/* Divider */.cG, null, "Interactive Elements"), /*#__PURE__*/react.createElement(es/* Space */.$x, {
    wrap: true
  }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary"
  }, "Primary Button"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "default"
  }, "Default Button"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "dashed"
  }, "Dashed Button"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "text"
  }, "Text Button"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "link"
  }, "Link Button"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    danger: true
  }, "Danger Button")), /*#__PURE__*/react.createElement(es/* Divider */.cG, null, "Tags and Badges"), /*#__PURE__*/react.createElement(es/* Space */.$x, {
    wrap: true
  }, /*#__PURE__*/react.createElement(es/* Tag */.vw, {
    color: "blue"
  }, "Blue Tag"), /*#__PURE__*/react.createElement(es/* Tag */.vw, {
    color: "green"
  }, "Green Tag"), /*#__PURE__*/react.createElement(es/* Tag */.vw, {
    color: "orange"
  }, "Orange Tag"), /*#__PURE__*/react.createElement(es/* Tag */.vw, {
    color: "red"
  }, "Red Tag"), /*#__PURE__*/react.createElement(es/* Tag */.vw, {
    color: "purple"
  }, "Purple Tag"), /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
    count: 5,
    style: {
      backgroundColor: '#52c41a'
    }
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      width: 40,
      height: 40,
      backgroundColor: 'var(--color-background-secondary)',
      border: '1px solid var(--color-border)',
      borderRadius: '4px'
    }
  }))), /*#__PURE__*/react.createElement(es/* Divider */.cG, null, "Alerts"), /*#__PURE__*/react.createElement(es/* Space */.$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
    message: "Success Alert",
    type: "success",
    showIcon: true
  }), /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
    message: "Info Alert",
    type: "info",
    showIcon: true
  }), /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
    message: "Warning Alert",
    type: "warning",
    showIcon: true
  }), /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
    message: "Error Alert",
    type: "error",
    showIcon: true
  })), /*#__PURE__*/react.createElement(es/* Divider */.cG, null, "Progress Indicators"), /*#__PURE__*/react.createElement(es/* Space */.$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement(es/* Progress */.ke, {
    percent: 30,
    status: "active"
  }), /*#__PURE__*/react.createElement(es/* Progress */.ke, {
    percent: 50,
    status: "normal"
  }), /*#__PURE__*/react.createElement(es/* Progress */.ke, {
    percent: 70,
    status: "exception"
  }), /*#__PURE__*/react.createElement(es/* Progress */.ke, {
    percent: 100
  })), /*#__PURE__*/react.createElement(es/* Divider */.cG, null, "Background Variations"), /*#__PURE__*/react.createElement(ContrastGrid, null, /*#__PURE__*/react.createElement(ContrastBox, {
    style: {
      backgroundColor: 'var(--color-surface)'
    }
  }, /*#__PURE__*/react.createElement(Text, {
    style: {
      color: 'var(--color-text)'
    }
  }, "Surface Background")), /*#__PURE__*/react.createElement(ContrastBox, {
    style: {
      backgroundColor: 'var(--color-background-secondary)'
    }
  }, /*#__PURE__*/react.createElement(Text, {
    style: {
      color: 'var(--color-text)'
    }
  }, "Secondary Background")), /*#__PURE__*/react.createElement(ContrastBox, {
    style: {
      backgroundColor: 'var(--color-background-tertiary)'
    }
  }, /*#__PURE__*/react.createElement(Text, {
    style: {
      color: 'var(--color-text)'
    }
  }, "Tertiary Background")), /*#__PURE__*/react.createElement(ContrastBox, {
    style: {
      backgroundColor: 'var(--color-primary)',
      color: 'white'
    }
  }, /*#__PURE__*/react.createElement(Text, {
    style: {
      color: 'white'
    }
  }, "Primary Background"))), /*#__PURE__*/react.createElement(es/* Divider */.cG, null, "Theme Information"), /*#__PURE__*/react.createElement("div", {
    style: {
      backgroundColor: 'var(--color-background-secondary)',
      padding: '16px',
      borderRadius: '8px',
      border: '1px solid var(--color-border-light)'
    }
  }, /*#__PURE__*/react.createElement(Text, {
    style: {
      color: 'var(--color-text)'
    }
  }, /*#__PURE__*/react.createElement("strong", null, "Current Theme:"), " ", isDarkMode ? 'Dark' : 'Light', " Mode", /*#__PURE__*/react.createElement("br", null), /*#__PURE__*/react.createElement("strong", null, "Theme Mode Setting:"), " ", themeMode, /*#__PURE__*/react.createElement("br", null), /*#__PURE__*/react.createElement("strong", null, "Enhanced Contrast:"), " ", enhancedContrast ? 'Enabled' : 'Disabled', /*#__PURE__*/react.createElement("br", null), /*#__PURE__*/react.createElement("strong", null, "Primary Color:"), " ", colors.primary, /*#__PURE__*/react.createElement("br", null), /*#__PURE__*/react.createElement("strong", null, "Background Color:"), " ", colors.background, /*#__PURE__*/react.createElement("br", null), /*#__PURE__*/react.createElement("strong", null, "Text Color:"), " ", colors.text)), /*#__PURE__*/react.createElement(es/* Divider */.cG, null, "Contrast Information"), /*#__PURE__*/react.createElement("div", {
    style: {
      backgroundColor: '#f9f9f9',
      padding: '16px',
      borderRadius: '8px',
      border: '1px solid #e8e8e8'
    }
  }, /*#__PURE__*/react.createElement(Text, null, /*#__PURE__*/react.createElement("strong", null, "Enhanced Contrast Features:")), /*#__PURE__*/react.createElement("ul", {
    style: {
      marginTop: '8px',
      paddingLeft: '20px'
    }
  }, /*#__PURE__*/react.createElement("li", null, "Primary text: 15.3:1 contrast ratio (WCAG AAA)"), /*#__PURE__*/react.createElement("li", null, "Secondary text: 7.2:1 contrast ratio (WCAG AAA)"), /*#__PURE__*/react.createElement("li", null, "Disabled text: 4.5:1 contrast ratio (WCAG AA)"), /*#__PURE__*/react.createElement("li", null, "Primary buttons: 5.9:1 contrast ratio (WCAG AA)"), /*#__PURE__*/react.createElement("li", null, "Status colors: 4.5:1+ contrast ratio (WCAG AA)"), /*#__PURE__*/react.createElement("li", null, "Links: Enhanced contrast with underlines"), /*#__PURE__*/react.createElement("li", null, "Focus indicators: High contrast outlines")))));
};
/* harmony default export */ const test_ContrastTest = (ContrastTest);

/***/ })

}]);
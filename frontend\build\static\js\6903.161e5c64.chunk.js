"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[6903],{

/***/ 11378:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(53986);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(23029);
/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(92901);
/* harmony import */ var _WebSocketService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(17053);




var _excluded = ["subtype"],
  _excluded2 = ["userId", "username", "status"],
  _excluded3 = ["userId"];
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * User Presence Service
 * 
 * This service manages user presence information, including online status,
 * activity status, and typing indicators.
 */


var UserPresenceService = /*#__PURE__*/function () {
  function UserPresenceService() {
    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(this, UserPresenceService);
    this.users = new Map(); // Map of user IDs to presence data
    this.currentUser = null; // Current user ID
    this.listeners = new Map(); // Event listeners
    this.typingTimeouts = new Map(); // Map of user IDs to typing timeout IDs
    this.typingInterval = null; // Interval for sending typing updates
    this.lastTypingUpdate = 0; // Timestamp of last typing update
    this.typingThrottleTime = 2000; // Minimum time between typing updates (ms)
    this.typingExpiryTime = 5000; // Time after which typing status expires (ms)
    this.initialized = false; // Whether the service has been initialized

    // Bind methods
    this.handlePresenceMessage = this.handlePresenceMessage.bind(this);
    this.handleUserJoined = this.handleUserJoined.bind(this);
    this.handleUserLeft = this.handleUserLeft.bind(this);
    this.handleUserActivity = this.handleUserActivity.bind(this);
    this.handleUserTyping = this.handleUserTyping.bind(this);
  }

  /**
   * Initialize the presence service
   * @param {Object} options - Initialization options
   * @param {string} options.userId - Current user ID
   * @param {string} options.username - Current username
   * @param {Object} options.userData - Additional user data
   * @returns {UserPresenceService} - Returns this instance for chaining
   */
  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(UserPresenceService, [{
    key: "init",
    value: function init() {
      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      var userId = options.userId,
        username = options.username,
        _options$userData = options.userData,
        userData = _options$userData === void 0 ? {} : _options$userData;
      if (!userId) {
        console.error('UserPresenceService: userId is required');
        return this;
      }

      // Set current user
      this.currentUser = userId;

      // Initialize WebSocket event listeners
      this.initWebSocketListeners();

      // Send initial presence message
      this.sendPresence(_objectSpread({
        userId: userId,
        username: username,
        status: 'online'
      }, userData));

      // Set up activity tracking
      this.initActivityTracking();

      // Set up page visibility tracking
      this.initVisibilityTracking();

      // Set initialized flag
      this.initialized = true;
      console.log("UserPresenceService initialized for user ".concat(userId));
      return this;
    }

    /**
     * Initialize WebSocket event listeners
     * @private
     */
  }, {
    key: "initWebSocketListeners",
    value: function initWebSocketListeners() {
      var _this = this;
      // Listen for presence messages
      _WebSocketService__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A.on('message', function (message) {
        if (message && message.type === 'presence') {
          _this.handlePresenceMessage(message);
        }
      });

      // Listen for connection events
      _WebSocketService__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A.on('connect', function () {
        // Re-send presence when reconnected
        if (_this.currentUser) {
          _this.sendPresence({
            userId: _this.currentUser,
            status: 'online'
          });
        }
      });
      _WebSocketService__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A.on('disconnect', function () {
        // Mark all users as offline when disconnected
        _this.users.forEach(function (user, userId) {
          if (userId !== _this.currentUser) {
            _this.updateUserPresence(userId, {
              status: 'offline'
            });
          }
        });
      });
    }

    /**
     * Initialize user activity tracking
     * @private
     */
  }, {
    key: "initActivityTracking",
    value: function initActivityTracking() {
      var _this2 = this;
      // Track user activity
      var activityEvents = ['mousedown', 'keydown', 'touchstart', 'scroll'];
      var handleActivity = function handleActivity() {
        if (_this2.currentUser) {
          _this2.updateActivity();
        }
      };
      activityEvents.forEach(function (event) {
        window.addEventListener(event, handleActivity, {
          passive: true
        });
      });
    }

    /**
     * Initialize page visibility tracking
     * @private
     */
  }, {
    key: "initVisibilityTracking",
    value: function initVisibilityTracking() {
      var _this3 = this;
      document.addEventListener('visibilitychange', function () {
        if (_this3.currentUser) {
          if (document.visibilityState === 'visible') {
            _this3.sendPresence({
              userId: _this3.currentUser,
              status: 'online'
            });
          } else {
            _this3.sendPresence({
              userId: _this3.currentUser,
              status: 'away'
            });
          }
        }
      });

      // Handle page unload
      window.addEventListener('beforeunload', function () {
        if (_this3.currentUser) {
          // Use sendBeacon if available for more reliable delivery
          if (navigator.sendBeacon) {
            var data = JSON.stringify({
              type: 'presence',
              userId: _this3.currentUser,
              status: 'offline'
            });
            navigator.sendBeacon('/api/presence', data);
          } else {
            // Fallback to synchronous XHR
            _this3.sendPresence({
              userId: _this3.currentUser,
              status: 'offline'
            }, true);
          }
        }
      });
    }

    /**
     * Handle presence message from WebSocket
     * @param {Object} message - Presence message
     * @private
     */
  }, {
    key: "handlePresenceMessage",
    value: function handlePresenceMessage(message) {
      var subtype = message.subtype,
        presenceData = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(message, _excluded);
      switch (subtype) {
        case 'user_joined':
          this.handleUserJoined(presenceData);
          break;
        case 'user_left':
          this.handleUserLeft(presenceData);
          break;
        case 'user_activity':
          this.handleUserActivity(presenceData);
          break;
        case 'user_typing':
          this.handleUserTyping(presenceData);
          break;
        case 'presence_update':
          this.handlePresenceUpdate(presenceData);
          break;
        case 'presence_list':
          this.handlePresenceList(presenceData);
          break;
        default:
          console.warn('Unknown presence subtype:', subtype);
      }
    }

    /**
     * Handle user joined event
     * @param {Object} data - User joined data
     * @private
     */
  }, {
    key: "handleUserJoined",
    value: function handleUserJoined(data) {
      var userId = data.userId,
        username = data.username,
        _data$status = data.status,
        status = _data$status === void 0 ? 'online' : _data$status,
        userData = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(data, _excluded2);
      if (!userId) return;

      // Add or update user
      this.updateUserPresence(userId, _objectSpread({
        userId: userId,
        username: username,
        status: status,
        lastSeen: new Date().toISOString()
      }, userData));

      // Notify listeners
      this.notifyListeners('user_joined', _objectSpread({
        userId: userId,
        username: username,
        status: status
      }, userData));
    }

    /**
     * Handle user left event
     * @param {Object} data - User left data
     * @private
     */
  }, {
    key: "handleUserLeft",
    value: function handleUserLeft(data) {
      var userId = data.userId;
      if (!userId) return;

      // Update user status
      this.updateUserPresence(userId, {
        status: 'offline',
        lastSeen: new Date().toISOString()
      });

      // Notify listeners
      this.notifyListeners('user_left', {
        userId: userId
      });
    }

    /**
     * Handle user activity event
     * @param {Object} data - User activity data
     * @private
     */
  }, {
    key: "handleUserActivity",
    value: function handleUserActivity(data) {
      var userId = data.userId,
        activity = data.activity,
        timestamp = data.timestamp;
      if (!userId) return;

      // Update user activity
      this.updateUserPresence(userId, {
        activity: activity,
        lastActivity: timestamp || new Date().toISOString()
      });

      // Notify listeners
      this.notifyListeners('user_activity', {
        userId: userId,
        activity: activity
      });
    }

    /**
     * Handle user typing event
     * @param {Object} data - User typing data
     * @private
     */
  }, {
    key: "handleUserTyping",
    value: function handleUserTyping(data) {
      var _this4 = this;
      var userId = data.userId,
        isTyping = data.isTyping,
        context = data.context;
      if (!userId) return;

      // Clear existing timeout
      if (this.typingTimeouts.has(userId)) {
        clearTimeout(this.typingTimeouts.get(userId));
      }

      // Update user typing status
      this.updateUserPresence(userId, {
        isTyping: isTyping,
        typingContext: context,
        lastTyping: new Date().toISOString()
      });

      // Set timeout to clear typing status
      if (isTyping) {
        var timeoutId = setTimeout(function () {
          _this4.updateUserPresence(userId, {
            isTyping: false
          });
          _this4.notifyListeners('user_typing', {
            userId: userId,
            isTyping: false,
            context: context
          });
          _this4.typingTimeouts["delete"](userId);
        }, this.typingExpiryTime);
        this.typingTimeouts.set(userId, timeoutId);
      }

      // Notify listeners
      this.notifyListeners('user_typing', {
        userId: userId,
        isTyping: isTyping,
        context: context
      });
    }

    /**
     * Handle presence update event
     * @param {Object} data - Presence update data
     * @private
     */
  }, {
    key: "handlePresenceUpdate",
    value: function handlePresenceUpdate(data) {
      var userId = data.userId,
        presenceData = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(data, _excluded3);
      if (!userId) return;

      // Update user presence
      this.updateUserPresence(userId, presenceData);

      // Notify listeners
      this.notifyListeners('presence_update', _objectSpread({
        userId: userId
      }, presenceData));
    }

    /**
     * Handle presence list event
     * @param {Object} data - Presence list data
     * @private
     */
  }, {
    key: "handlePresenceList",
    value: function handlePresenceList(data) {
      var _this5 = this;
      var _data$users = data.users,
        users = _data$users === void 0 ? [] : _data$users;

      // Update all users
      users.forEach(function (user) {
        if (user.userId) {
          _this5.updateUserPresence(user.userId, user);
        }
      });

      // Notify listeners
      this.notifyListeners('presence_list', {
        users: users
      });
    }

    /**
     * Update user presence data
     * @param {string} userId - User ID
     * @param {Object} data - Presence data to update
     * @private
     */
  }, {
    key: "updateUserPresence",
    value: function updateUserPresence(userId, data) {
      // Get existing user data or create new
      var userData = this.users.get(userId) || {
        userId: userId
      };

      // Update user data
      this.users.set(userId, _objectSpread(_objectSpread(_objectSpread({}, userData), data), {}, {
        lastUpdated: new Date().toISOString()
      }));
    }

    /**
     * Send presence update
     * @param {Object} data - Presence data
     * @param {boolean} sync - Whether to send synchronously (default: false)
     */
  }, {
    key: "sendPresence",
    value: function sendPresence(data) {
      var sync = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
      if (!_WebSocketService__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A.isConnected && !sync) {
        console.warn('WebSocket not connected, presence update will be queued');
      }
      var message = _objectSpread({
        type: 'presence',
        subtype: 'presence_update',
        timestamp: new Date().toISOString()
      }, data);
      if (sync) {
        // Send synchronously using XHR
        try {
          var xhr = new XMLHttpRequest();
          xhr.open('POST', '/api/presence', false); // false for synchronous
          xhr.setRequestHeader('Content-Type', 'application/json');
          xhr.send(JSON.stringify(message));
        } catch (error) {
          console.error('Error sending synchronous presence update:', error);
        }
      } else {
        // Send via WebSocket
        _WebSocketService__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A.send(message, {
          queueIfOffline: true,
          persistIfOffline: false // No need to persist presence messages
        });
      }

      // Update local user data
      if (data.userId) {
        this.updateUserPresence(data.userId, data);
      }
    }

    /**
     * Update user activity
     * @param {Object} data - Activity data
     */
  }, {
    key: "updateActivity",
    value: function updateActivity() {
      var data = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      if (!this.currentUser) return;
      this.sendPresence(_objectSpread({
        userId: this.currentUser,
        status: 'online',
        lastActivity: new Date().toISOString()
      }, data));
    }

    /**
     * Update typing status
     * @param {boolean} isTyping - Whether the user is typing
     * @param {string} context - Context identifier (e.g., chat room ID)
     */
  }, {
    key: "updateTyping",
    value: function updateTyping(isTyping, context) {
      var _this6 = this;
      if (!this.currentUser) return;
      var now = Date.now();

      // Throttle typing updates
      if (isTyping && now - this.lastTypingUpdate < this.typingThrottleTime) {
        // If we're already sending periodic updates, don't send another one yet
        if (this.typingInterval) return;

        // Set up interval to send periodic updates
        if (!this.typingInterval) {
          this.typingInterval = setInterval(function () {
            _this6.sendTypingUpdate(true, context);
          }, this.typingThrottleTime);
        }
        return;
      }

      // Send typing update
      this.sendTypingUpdate(isTyping, context);

      // Clear typing interval if not typing
      if (!isTyping && this.typingInterval) {
        clearInterval(this.typingInterval);
        this.typingInterval = null;
      }
    }

    /**
     * Send typing update
     * @param {boolean} isTyping - Whether the user is typing
     * @param {string} context - Context identifier
     * @private
     */
  }, {
    key: "sendTypingUpdate",
    value: function sendTypingUpdate(isTyping, context) {
      this.lastTypingUpdate = Date.now();
      this.sendPresence({
        userId: this.currentUser,
        subtype: 'user_typing',
        isTyping: isTyping,
        context: context,
        timestamp: new Date().toISOString()
      });
    }

    /**
     * Get all online users
     * @returns {Array} - Array of online users
     */
  }, {
    key: "getOnlineUsers",
    value: function getOnlineUsers() {
      var onlineUsers = [];
      this.users.forEach(function (user) {
        if (user.status === 'online' || user.status === 'away') {
          onlineUsers.push(user);
        }
      });
      return onlineUsers;
    }

    /**
     * Get user by ID
     * @param {string} userId - User ID
     * @returns {Object|null} - User data or null if not found
     */
  }, {
    key: "getUser",
    value: function getUser(userId) {
      return this.users.get(userId) || null;
    }

    /**
     * Check if user is online
     * @param {string} userId - User ID
     * @returns {boolean} - Whether the user is online
     */
  }, {
    key: "isUserOnline",
    value: function isUserOnline(userId) {
      var user = this.users.get(userId);
      return user && (user.status === 'online' || user.status === 'away');
    }

    /**
     * Check if user is typing
     * @param {string} userId - User ID
     * @param {string} context - Context identifier (optional)
     * @returns {boolean} - Whether the user is typing
     */
  }, {
    key: "isUserTyping",
    value: function isUserTyping(userId, context) {
      var user = this.users.get(userId);
      if (!user || !user.isTyping) return false;

      // If context is provided, check if user is typing in that context
      if (context && user.typingContext !== context) return false;
      return true;
    }

    /**
     * Add event listener
     * @param {string} event - Event name
     * @param {Function} callback - Callback function
     */
  }, {
    key: "on",
    value: function on(event, callback) {
      if (!this.listeners.has(event)) {
        this.listeners.set(event, []);
      }
      this.listeners.get(event).push(callback);
    }

    /**
     * Remove event listener
     * @param {string} event - Event name
     * @param {Function} callback - Callback function
     */
  }, {
    key: "off",
    value: function off(event, callback) {
      if (!this.listeners.has(event)) return;
      var callbacks = this.listeners.get(event);
      var index = callbacks.indexOf(callback);
      if (index !== -1) {
        callbacks.splice(index, 1);
      }
    }

    /**
     * Notify listeners of an event
     * @param {string} event - Event name
     * @param {Object} data - Event data
     * @private
     */
  }, {
    key: "notifyListeners",
    value: function notifyListeners(event, data) {
      if (!this.listeners.has(event)) return;
      var callbacks = this.listeners.get(event);
      callbacks.forEach(function (callback) {
        try {
          callback(data);
        } catch (error) {
          console.error("Error in ".concat(event, " listener:"), error);
        }
      });
    }

    /**
     * Clean up resources
     */
  }, {
    key: "cleanup",
    value: function cleanup() {
      // Clear all timeouts
      this.typingTimeouts.forEach(function (timeoutId) {
        clearTimeout(timeoutId);
      });

      // Clear typing interval
      if (this.typingInterval) {
        clearInterval(this.typingInterval);
      }

      // Send offline status
      if (this.currentUser) {
        this.sendPresence({
          userId: this.currentUser,
          status: 'offline'
        });
      }

      // Clear data
      this.users.clear();
      this.typingTimeouts.clear();
      this.currentUser = null;
      this.initialized = false;
    }
  }]);
}(); // Create singleton instance
var userPresenceService = new UserPresenceService();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (userPresenceService);

/***/ }),

/***/ 49645:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(53986);
/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(23029);
/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(92901);
/* harmony import */ var _WebSocketService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(17053);
/* harmony import */ var _UserPresenceService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(11378);




var _excluded = ["subtype", "documentId"];
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Shared Editing Service
 * 
 * This service provides collaborative editing functionality using operational
 * transformation to synchronize changes between users.
 */



var SharedEditingService = /*#__PURE__*/function () {
  function SharedEditingService() {
    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(this, SharedEditingService);
    this.documents = new Map(); // Map of document IDs to document data
    this.activeDocument = null; // Currently active document ID
    this.listeners = new Map(); // Event listeners
    this.pendingOperations = new Map(); // Map of document IDs to pending operations
    this.revisions = new Map(); // Map of document IDs to revision numbers
    this.initialized = false; // Whether the service has been initialized
    this.userId = null; // Current user ID
    this.username = null; // Current username

    // Bind methods
    this.handleDocumentMessage = this.handleDocumentMessage.bind(this);
    this.handleDocumentUpdate = this.handleDocumentUpdate.bind(this);
    this.handleDocumentCursor = this.handleDocumentCursor.bind(this);
    this.handleDocumentSelection = this.handleDocumentSelection.bind(this);
  }

  /**
   * Initialize the shared editing service
   * @param {Object} options - Initialization options
   * @param {string} options.userId - Current user ID
   * @param {string} options.username - Current username
   * @returns {SharedEditingService} - Returns this instance for chaining
   */
  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(SharedEditingService, [{
    key: "init",
    value: function init() {
      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      var userId = options.userId,
        username = options.username;
      if (!userId) {
        console.error('SharedEditingService: userId is required');
        return this;
      }

      // Set user info
      this.userId = userId;
      this.username = username || userId;

      // Initialize WebSocket event listeners
      this.initWebSocketListeners();

      // Set initialized flag
      this.initialized = true;
      console.log("SharedEditingService initialized for user ".concat(userId));
      return this;
    }

    /**
     * Initialize WebSocket event listeners
     * @private
     */
  }, {
    key: "initWebSocketListeners",
    value: function initWebSocketListeners() {
      var _this = this;
      // Listen for document messages
      _WebSocketService__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A.on('message', function (message) {
        if (message && message.type === 'document') {
          _this.handleDocumentMessage(message);
        }
      });

      // Listen for connection events
      _WebSocketService__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A.on('connect', function () {
        // Re-join active document when reconnected
        if (_this.activeDocument) {
          _this.joinDocument(_this.activeDocument);
        }
      });
    }

    /**
     * Handle document message from WebSocket
     * @param {Object} message - Document message
     * @private
     */
  }, {
    key: "handleDocumentMessage",
    value: function handleDocumentMessage(message) {
      var subtype = message.subtype,
        documentId = message.documentId,
        data = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(message, _excluded);
      if (!documentId) {
        console.warn('Document message missing documentId:', message);
        return;
      }
      switch (subtype) {
        case 'document_update':
          this.handleDocumentUpdate(documentId, data);
          break;
        case 'document_cursor':
          this.handleDocumentCursor(documentId, data);
          break;
        case 'document_selection':
          this.handleDocumentSelection(documentId, data);
          break;
        case 'document_join':
          this.handleDocumentJoin(documentId, data);
          break;
        case 'document_leave':
          this.handleDocumentLeave(documentId, data);
          break;
        case 'document_sync':
          this.handleDocumentSync(documentId, data);
          break;
        default:
          console.warn('Unknown document subtype:', subtype);
      }
    }

    /**
     * Handle document update event
     * @param {string} documentId - Document ID
     * @param {Object} data - Update data
     * @private
     */
  }, {
    key: "handleDocumentUpdate",
    value: function handleDocumentUpdate(documentId, data) {
      var operations = data.operations,
        revision = data.revision,
        userId = data.userId;

      // Ignore our own updates
      if (userId === this.userId) return;

      // Get document
      var document = this.getDocument(documentId);
      if (!document) {
        console.warn("Document ".concat(documentId, " not found for update"));
        return;
      }

      // Check revision
      var currentRevision = this.revisions.get(documentId) || 0;
      if (revision <= currentRevision) {
        console.warn("Ignoring outdated update for document ".concat(documentId, " (received: ").concat(revision, ", current: ").concat(currentRevision, ")"));
        return;
      }

      // Apply operations
      if (operations && operations.length > 0) {
        // Apply operations to document content
        var newContent = this.applyOperations(document.content, operations);

        // Update document
        this.updateDocument(documentId, {
          content: newContent,
          lastUpdated: new Date().toISOString(),
          lastUpdatedBy: userId
        });

        // Update revision
        this.revisions.set(documentId, revision);
      }

      // Notify listeners
      this.notifyListeners('document_update', {
        documentId: documentId,
        operations: operations,
        revision: revision,
        userId: userId
      });
    }

    /**
     * Handle document cursor event
     * @param {string} documentId - Document ID
     * @param {Object} data - Cursor data
     * @private
     */
  }, {
    key: "handleDocumentCursor",
    value: function handleDocumentCursor(documentId, data) {
      var userId = data.userId,
        position = data.position;

      // Ignore our own cursor updates
      if (userId === this.userId) return;

      // Get document
      var document = this.getDocument(documentId);
      if (!document) {
        console.warn("Document ".concat(documentId, " not found for cursor update"));
        return;
      }

      // Update cursors
      if (!document.cursors) {
        document.cursors = {};
      }
      document.cursors[userId] = {
        position: position,
        timestamp: new Date().toISOString()
      };

      // Notify listeners
      this.notifyListeners('document_cursor', {
        documentId: documentId,
        userId: userId,
        position: position
      });
    }

    /**
     * Handle document selection event
     * @param {string} documentId - Document ID
     * @param {Object} data - Selection data
     * @private
     */
  }, {
    key: "handleDocumentSelection",
    value: function handleDocumentSelection(documentId, data) {
      var userId = data.userId,
        selection = data.selection;

      // Ignore our own selection updates
      if (userId === this.userId) return;

      // Get document
      var document = this.getDocument(documentId);
      if (!document) {
        console.warn("Document ".concat(documentId, " not found for selection update"));
        return;
      }

      // Update selections
      if (!document.selections) {
        document.selections = {};
      }
      document.selections[userId] = _objectSpread(_objectSpread({}, selection), {}, {
        timestamp: new Date().toISOString()
      });

      // Notify listeners
      this.notifyListeners('document_selection', {
        documentId: documentId,
        userId: userId,
        selection: selection
      });
    }

    /**
     * Handle document join event
     * @param {string} documentId - Document ID
     * @param {Object} data - Join data
     * @private
     */
  }, {
    key: "handleDocumentJoin",
    value: function handleDocumentJoin(documentId, data) {
      var userId = data.userId,
        username = data.username;

      // Get document
      var document = this.getDocument(documentId);
      if (!document) {
        console.warn("Document ".concat(documentId, " not found for join event"));
        return;
      }

      // Update collaborators
      if (!document.collaborators) {
        document.collaborators = {};
      }
      document.collaborators[userId] = {
        userId: userId,
        username: username,
        joinedAt: new Date().toISOString()
      };

      // Notify listeners
      this.notifyListeners('document_join', {
        documentId: documentId,
        userId: userId,
        username: username
      });
    }

    /**
     * Handle document leave event
     * @param {string} documentId - Document ID
     * @param {Object} data - Leave data
     * @private
     */
  }, {
    key: "handleDocumentLeave",
    value: function handleDocumentLeave(documentId, data) {
      var userId = data.userId;

      // Get document
      var document = this.getDocument(documentId);
      if (!document) {
        console.warn("Document ".concat(documentId, " not found for leave event"));
        return;
      }

      // Update collaborators
      if (document.collaborators && document.collaborators[userId]) {
        delete document.collaborators[userId];
      }

      // Remove cursor
      if (document.cursors && document.cursors[userId]) {
        delete document.cursors[userId];
      }

      // Remove selection
      if (document.selections && document.selections[userId]) {
        delete document.selections[userId];
      }

      // Notify listeners
      this.notifyListeners('document_leave', {
        documentId: documentId,
        userId: userId
      });
    }

    /**
     * Handle document sync event
     * @param {string} documentId - Document ID
     * @param {Object} data - Sync data
     * @private
     */
  }, {
    key: "handleDocumentSync",
    value: function handleDocumentSync(documentId, data) {
      var content = data.content,
        revision = data.revision,
        collaborators = data.collaborators;

      // Create or update document
      var document = this.getDocument(documentId) || {
        id: documentId,
        content: '',
        collaborators: {},
        cursors: {},
        selections: {}
      };

      // Update document
      document.content = content;
      document.lastSynced = new Date().toISOString();

      // Update revision
      this.revisions.set(documentId, revision);

      // Update collaborators
      if (collaborators) {
        document.collaborators = collaborators;
      }

      // Save document
      this.documents.set(documentId, document);

      // Notify listeners
      this.notifyListeners('document_sync', {
        documentId: documentId,
        content: content,
        revision: revision,
        collaborators: collaborators
      });
    }

    /**
     * Join a document
     * @param {string} documentId - Document ID
     * @param {Object} options - Join options
     * @param {boolean} options.requestSync - Whether to request a sync (default: true)
     * @returns {Object} - Document data
     */
  }, {
    key: "joinDocument",
    value: function joinDocument(documentId) {
      var _this2 = this;
      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var _options$requestSync = options.requestSync,
        requestSync = _options$requestSync === void 0 ? true : _options$requestSync;
      if (!this.initialized) {
        console.error('SharedEditingService not initialized');
        return null;
      }

      // Set as active document
      this.activeDocument = documentId;

      // Get or create document
      var document = this.getDocument(documentId);
      if (!document) {
        document = {
          id: documentId,
          content: '',
          collaborators: {},
          cursors: {},
          selections: {}
        };
        this.documents.set(documentId, document);
      }

      // Send join message
      this.sendDocumentMessage({
        subtype: 'document_join',
        documentId: documentId,
        userId: this.userId,
        username: this.username
      });

      // Request sync if needed
      if (requestSync) {
        this.sendDocumentMessage({
          subtype: 'document_sync_request',
          documentId: documentId
        });
      }

      // Start typing indicator integration
      _UserPresenceService__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A.on('user_typing', function (data) {
        var userId = data.userId,
          isTyping = data.isTyping,
          context = data.context;

        // Only handle typing events for the active document
        if (context === documentId) {
          _this2.notifyListeners('user_typing', {
            documentId: documentId,
            userId: userId,
            isTyping: isTyping
          });
        }
      });
      return document;
    }

    /**
     * Leave a document
     * @param {string} documentId - Document ID
     */
  }, {
    key: "leaveDocument",
    value: function leaveDocument(documentId) {
      if (!this.initialized) {
        console.error('SharedEditingService not initialized');
        return;
      }

      // Send leave message
      this.sendDocumentMessage({
        subtype: 'document_leave',
        documentId: documentId,
        userId: this.userId
      });

      // Clear active document if it's the one we're leaving
      if (this.activeDocument === documentId) {
        this.activeDocument = null;
      }
    }

    /**
     * Update document content
     * @param {string} documentId - Document ID
     * @param {string} content - New content
     * @param {Array} operations - Operations that led to this content
     */
  }, {
    key: "updateContent",
    value: function updateContent(documentId, content) {
      var operations = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];
      if (!this.initialized) {
        console.error('SharedEditingService not initialized');
        return;
      }

      // Get document
      var document = this.getDocument(documentId);
      if (!document) {
        console.warn("Document ".concat(documentId, " not found for content update"));
        return;
      }

      // Update document content
      this.updateDocument(documentId, {
        content: content,
        lastUpdated: new Date().toISOString(),
        lastUpdatedBy: this.userId
      });

      // Get current revision
      var currentRevision = this.revisions.get(documentId) || 0;
      var newRevision = currentRevision + 1;

      // Update revision
      this.revisions.set(documentId, newRevision);

      // Send update message
      this.sendDocumentMessage({
        subtype: 'document_update',
        documentId: documentId,
        userId: this.userId,
        operations: operations,
        revision: newRevision
      });

      // Update typing status
      if (operations.length > 0) {
        _UserPresenceService__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A.updateTyping(true, documentId);

        // Clear typing status after a delay
        setTimeout(function () {
          _UserPresenceService__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A.updateTyping(false, documentId);
        }, 2000);
      }
    }

    /**
     * Update cursor position
     * @param {string} documentId - Document ID
     * @param {number} position - Cursor position
     */
  }, {
    key: "updateCursor",
    value: function updateCursor(documentId, position) {
      if (!this.initialized) {
        console.error('SharedEditingService not initialized');
        return;
      }

      // Get document
      var document = this.getDocument(documentId);
      if (!document) {
        console.warn("Document ".concat(documentId, " not found for cursor update"));
        return;
      }

      // Update cursors
      if (!document.cursors) {
        document.cursors = {};
      }
      document.cursors[this.userId] = {
        position: position,
        timestamp: new Date().toISOString()
      };

      // Send cursor message
      this.sendDocumentMessage({
        subtype: 'document_cursor',
        documentId: documentId,
        userId: this.userId,
        position: position
      });
    }

    /**
     * Update selection
     * @param {string} documentId - Document ID
     * @param {Object} selection - Selection data
     * @param {number} selection.start - Selection start
     * @param {number} selection.end - Selection end
     */
  }, {
    key: "updateSelection",
    value: function updateSelection(documentId, selection) {
      if (!this.initialized) {
        console.error('SharedEditingService not initialized');
        return;
      }

      // Get document
      var document = this.getDocument(documentId);
      if (!document) {
        console.warn("Document ".concat(documentId, " not found for selection update"));
        return;
      }

      // Update selections
      if (!document.selections) {
        document.selections = {};
      }
      document.selections[this.userId] = _objectSpread(_objectSpread({}, selection), {}, {
        timestamp: new Date().toISOString()
      });

      // Send selection message
      this.sendDocumentMessage({
        subtype: 'document_selection',
        documentId: documentId,
        userId: this.userId,
        selection: selection
      });
    }

    /**
     * Get document by ID
     * @param {string} documentId - Document ID
     * @returns {Object|null} - Document data or null if not found
     */
  }, {
    key: "getDocument",
    value: function getDocument(documentId) {
      return this.documents.get(documentId) || null;
    }

    /**
     * Update document data
     * @param {string} documentId - Document ID
     * @param {Object} data - Document data to update
     * @private
     */
  }, {
    key: "updateDocument",
    value: function updateDocument(documentId, data) {
      // Get existing document data or create new
      var documentData = this.documents.get(documentId) || {
        id: documentId
      };

      // Update document data
      this.documents.set(documentId, _objectSpread(_objectSpread({}, documentData), data));
    }

    /**
     * Send document message via WebSocket
     * @param {Object} message - Message to send
     * @private
     */
  }, {
    key: "sendDocumentMessage",
    value: function sendDocumentMessage(message) {
      _WebSocketService__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A.send(_objectSpread({
        type: 'document'
      }, message), {
        queueIfOffline: true,
        persistIfOffline: true
      });
    }

    /**
     * Apply operations to content
     * @param {string} content - Original content
     * @param {Array} operations - Operations to apply
     * @returns {string} - New content
     * @private
     */
  }, {
    key: "applyOperations",
    value: function applyOperations(content, operations) {
      var _this3 = this;
      if (!operations || operations.length === 0) {
        return content;
      }
      var result = content;
      operations.forEach(function (op) {
        switch (op.type) {
          case 'insert':
            result = _this3.applyInsert(result, op.position, op.text);
            break;
          case 'delete':
            result = _this3.applyDelete(result, op.position, op.length);
            break;
          case 'replace':
            result = _this3.applyReplace(result, op.position, op.length, op.text);
            break;
          default:
            console.warn('Unknown operation type:', op.type);
        }
      });
      return result;
    }

    /**
     * Apply insert operation
     * @param {string} content - Original content
     * @param {number} position - Insert position
     * @param {string} text - Text to insert
     * @returns {string} - New content
     * @private
     */
  }, {
    key: "applyInsert",
    value: function applyInsert(content, position, text) {
      if (position < 0 || position > content.length) {
        console.warn("Invalid insert position: ".concat(position, " (content length: ").concat(content.length, ")"));
        return content;
      }
      return content.slice(0, position) + text + content.slice(position);
    }

    /**
     * Apply delete operation
     * @param {string} content - Original content
     * @param {number} position - Delete position
     * @param {number} length - Length to delete
     * @returns {string} - New content
     * @private
     */
  }, {
    key: "applyDelete",
    value: function applyDelete(content, position, length) {
      if (position < 0 || position + length > content.length) {
        console.warn("Invalid delete range: ".concat(position, "-").concat(position + length, " (content length: ").concat(content.length, ")"));
        return content;
      }
      return content.slice(0, position) + content.slice(position + length);
    }

    /**
     * Apply replace operation
     * @param {string} content - Original content
     * @param {number} position - Replace position
     * @param {number} length - Length to replace
     * @param {string} text - Replacement text
     * @returns {string} - New content
     * @private
     */
  }, {
    key: "applyReplace",
    value: function applyReplace(content, position, length, text) {
      if (position < 0 || position + length > content.length) {
        console.warn("Invalid replace range: ".concat(position, "-").concat(position + length, " (content length: ").concat(content.length, ")"));
        return content;
      }
      return content.slice(0, position) + text + content.slice(position + length);
    }

    /**
     * Add event listener
     * @param {string} event - Event name
     * @param {Function} callback - Callback function
     */
  }, {
    key: "on",
    value: function on(event, callback) {
      if (!this.listeners.has(event)) {
        this.listeners.set(event, []);
      }
      this.listeners.get(event).push(callback);
    }

    /**
     * Remove event listener
     * @param {string} event - Event name
     * @param {Function} callback - Callback function
     */
  }, {
    key: "off",
    value: function off(event, callback) {
      if (!this.listeners.has(event)) return;
      var callbacks = this.listeners.get(event);
      var index = callbacks.indexOf(callback);
      if (index !== -1) {
        callbacks.splice(index, 1);
      }
    }

    /**
     * Notify listeners of an event
     * @param {string} event - Event name
     * @param {Object} data - Event data
     * @private
     */
  }, {
    key: "notifyListeners",
    value: function notifyListeners(event, data) {
      if (!this.listeners.has(event)) return;
      var callbacks = this.listeners.get(event);
      callbacks.forEach(function (callback) {
        try {
          callback(data);
        } catch (error) {
          console.error("Error in ".concat(event, " listener:"), error);
        }
      });
    }

    /**
     * Clean up resources
     */
  }, {
    key: "cleanup",
    value: function cleanup() {
      // Leave active document
      if (this.activeDocument) {
        this.leaveDocument(this.activeDocument);
      }

      // Clear data
      this.documents.clear();
      this.pendingOperations.clear();
      this.revisions.clear();
      this.activeDocument = null;
      this.initialized = false;
    }
  }]);
}(); // Create singleton instance
var sharedEditingService = new SharedEditingService();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (sharedEditingService);

/***/ }),

/***/ 86132:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);
/* harmony import */ var _services_UserPresenceService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(11378);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




var Text = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Text;

/**
 * Get status color based on user status
 * @param {string} status - User status
 * @returns {string} - Status color
 */
var getStatusColor = function getStatusColor(status) {
  switch (status) {
    case 'online':
      return '#52c41a';
    // green
    case 'away':
      return '#faad14';
    // yellow
    case 'busy':
      return '#f5222d';
    // red
    case 'offline':
    default:
      return '#d9d9d9';
    // grey
  }
};

/**
 * Get status text based on user status
 * @param {string} status - User status
 * @returns {string} - Status text
 */
var getStatusText = function getStatusText(status) {
  switch (status) {
    case 'online':
      return 'Online';
    case 'away':
      return 'Away';
    case 'busy':
      return 'Busy';
    case 'offline':
    default:
      return 'Offline';
  }
};

/**
 * UserPresenceIndicator component
 * Displays a user's presence status
 */
var UserPresenceIndicator = function UserPresenceIndicator(_ref) {
  var userId = _ref.userId,
    username = _ref.username,
    _ref$showUsername = _ref.showUsername,
    showUsername = _ref$showUsername === void 0 ? true : _ref$showUsername,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'default' : _ref$size,
    _ref$avatarSize = _ref.avatarSize,
    avatarSize = _ref$avatarSize === void 0 ? 24 : _ref$avatarSize,
    _ref$showTooltip = _ref.showTooltip,
    showTooltip = _ref$showTooltip === void 0 ? true : _ref$showTooltip,
    _ref$style = _ref.style,
    style = _ref$style === void 0 ? {} : _ref$style;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    user = _useState2[0],
    setUser = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    isTyping = _useState4[0],
    setIsTyping = _useState4[1];

  // Get user data on mount and when userId changes
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (!userId) return;

    // Get initial user data
    var userData = _services_UserPresenceService__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A.getUser(userId);
    setUser(userData);

    // Check if user is typing
    setIsTyping(_services_UserPresenceService__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A.isUserTyping(userId));

    // Listen for presence updates
    var handlePresenceUpdate = function handlePresenceUpdate(data) {
      if (data.userId === userId) {
        setUser(_services_UserPresenceService__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A.getUser(userId));
      }
    };

    // Listen for typing updates
    var handleTypingUpdate = function handleTypingUpdate(data) {
      if (data.userId === userId) {
        setIsTyping(data.isTyping);
      }
    };

    // Register event listeners
    _services_UserPresenceService__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A.on('presence_update', handlePresenceUpdate);
    _services_UserPresenceService__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A.on('user_typing', handleTypingUpdate);

    // Clean up event listeners
    return function () {
      _services_UserPresenceService__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A.off('presence_update', handlePresenceUpdate);
      _services_UserPresenceService__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A.off('user_typing', handleTypingUpdate);
    };
  }, [userId]);

  // If no user data, show placeholder
  if (!user) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
      style: _objectSpread({}, style)
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Avatar */ .eu, {
      size: avatarSize,
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .UserOutlined */ .qmv, null)
    }), showUsername && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, null, username || userId || 'Unknown User'));
  }

  // Get status color and text
  var statusColor = getStatusColor(user.status);
  var statusText = getStatusText(user.status);

  // Create tooltip content
  var tooltipContent = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, user.username || userId), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, "Status: ", statusText), isTyping && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, "Currently typing..."), user.lastSeen && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, "Last seen: ", new Date(user.lastSeen).toLocaleString()));

  // Create avatar with status badge
  var avatarWithBadge = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Badge */ .Ex, {
    dot: size === 'small',
    status: isTyping ? 'processing' : undefined,
    color: statusColor,
    offset: [-4, 4]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Avatar */ .eu, {
    size: avatarSize,
    src: user.avatar,
    style: {
      backgroundColor: user.avatar ? undefined : statusColor
    }
  }, !user.avatar && (user.username ? user.username[0].toUpperCase() : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .UserOutlined */ .qmv, null))));

  // Wrap in tooltip if showTooltip is true
  var avatarElement = showTooltip ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
    title: tooltipContent,
    placement: "top"
  }, avatarWithBadge) : avatarWithBadge;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
    style: _objectSpread({}, style)
  }, avatarElement, showUsername && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, null, user.username || userId, isTyping && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    type: "secondary",
    italic: true
  }, " typing...")));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UserPresenceIndicator);

/***/ }),

/***/ 96903:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var react_quill__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(45708);
/* harmony import */ var react_quill__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_quill__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react_quill_dist_quill_snow_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(8144);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(35346);
/* harmony import */ var _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(49645);
/* harmony import */ var _services_UserPresenceService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(11378);
/* harmony import */ var _UserPresenceIndicator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(86132);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }








var Text = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Text;

/**
 * SharedEditor component
 * Provides collaborative text editing functionality
 */
var SharedEditor = function SharedEditor(_ref) {
  var documentId = _ref.documentId,
    _ref$title = _ref.title,
    title = _ref$title === void 0 ? 'Shared Document' : _ref$title,
    _ref$height = _ref.height,
    height = _ref$height === void 0 ? 300 : _ref$height,
    _ref$readOnly = _ref.readOnly,
    readOnly = _ref$readOnly === void 0 ? false : _ref$readOnly,
    userId = _ref.userId,
    username = _ref.username,
    onContentChange = _ref.onContentChange,
    _ref$style = _ref.style,
    style = _ref$style === void 0 ? {} : _ref$style;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    content = _useState2[0],
    setContent = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({}),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    collaborators = _useState4[0],
    setCollaborators = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    isJoined = _useState6[0],
    setIsJoined = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    isSyncing = _useState8[0],
    setIsSyncing = _useState8[1];
  var editorRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);
  var lastContentChangeTime = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(0);
  var operationsQueue = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)([]);
  var lastCursorPosition = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(0);
  var lastSelectionRange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)({
    start: 0,
    end: 0
  });

  // Initialize services and join document
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (!documentId || !userId) return;

    // Initialize services if needed
    if (!_services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.initialized) {
      _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.init({
        userId: userId,
        username: username
      });
    }
    if (!_services_UserPresenceService__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A.initialized) {
      _services_UserPresenceService__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A.init({
        userId: userId,
        username: username
      });
    }

    // Join document
    setIsSyncing(true);
    var document = _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.joinDocument(documentId);
    setIsJoined(true);

    // Set initial content if available
    if (document && document.content) {
      setContent(document.content);
    }

    // Set initial collaborators if available
    if (document && document.collaborators) {
      setCollaborators(document.collaborators);
    }
    setIsSyncing(false);

    // Clean up on unmount
    return function () {
      if (isJoined) {
        _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.leaveDocument(documentId);
        setIsJoined(false);
      }
    };
  }, [documentId, userId, username]);

  // Listen for document updates
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (!documentId) return;

    // Handle document update
    var handleDocumentUpdate = function handleDocumentUpdate(data) {
      if (data.documentId !== documentId) return;

      // Get document
      var document = _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.getDocument(documentId);
      if (document) {
        // Update content
        setContent(document.content);

        // Call onContentChange callback if provided
        if (onContentChange) {
          onContentChange(document.content);
        }
      }
    };

    // Handle document sync
    var handleDocumentSync = function handleDocumentSync(data) {
      if (data.documentId !== documentId) return;
      setIsSyncing(false);

      // Get document
      var document = _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.getDocument(documentId);
      if (document) {
        // Update content
        setContent(document.content);

        // Update collaborators
        if (document.collaborators) {
          setCollaborators(document.collaborators);
        }

        // Call onContentChange callback if provided
        if (onContentChange) {
          onContentChange(document.content);
        }
      }
    };

    // Handle document join
    var handleDocumentJoin = function handleDocumentJoin(data) {
      if (data.documentId !== documentId) return;

      // Get document
      var document = _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.getDocument(documentId);
      if (document && document.collaborators) {
        setCollaborators(document.collaborators);
      }
    };

    // Handle document leave
    var handleDocumentLeave = function handleDocumentLeave(data) {
      if (data.documentId !== documentId) return;

      // Get document
      var document = _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.getDocument(documentId);
      if (document && document.collaborators) {
        setCollaborators(document.collaborators);
      }
    };

    // Register event listeners
    _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.on('document_update', handleDocumentUpdate);
    _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.on('document_sync', handleDocumentSync);
    _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.on('document_join', handleDocumentJoin);
    _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.on('document_leave', handleDocumentLeave);

    // Clean up event listeners
    return function () {
      _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.off('document_update', handleDocumentUpdate);
      _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.off('document_sync', handleDocumentSync);
      _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.off('document_join', handleDocumentJoin);
      _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.off('document_leave', handleDocumentLeave);
    };
  }, [documentId, onContentChange]);

  // Handle content change
  var handleContentChange = function handleContentChange(newContent) {
    var oldContent = content;

    // Update local content
    setContent(newContent);

    // Call onContentChange callback if provided
    if (onContentChange) {
      onContentChange(newContent);
    }

    // Generate operations (would need to adapt for Quill delta format)
    var operations = generateOperations(oldContent, newContent);

    // Add operations to queue
    if (operations.length > 0) {
      operationsQueue.current = [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(operationsQueue.current), (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(operations));
    }

    // Throttle updates
    var now = Date.now();
    if (now - lastContentChangeTime.current > 500) {
      sendContentUpdate();
      lastContentChangeTime.current = now;
    } else {
      setTimeout(function () {
        sendContentUpdate();
      }, 500);
    }
  };

  // Send content update
  var sendContentUpdate = function sendContentUpdate() {
    if (operationsQueue.current.length === 0) return;

    // Send update
    _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.updateContent(documentId, content, operationsQueue.current);

    // Clear operations queue
    operationsQueue.current = [];

    // Update last content change time
    lastContentChangeTime.current = Date.now();
  };

  // Generate operations from old and new content
  var generateOperations = function generateOperations(oldContent, newContent) {
    // Simple diff algorithm - this could be improved with a proper diff library
    if (oldContent === newContent) return [];

    // For simplicity, just create a replace operation
    return [{
      type: 'replace',
      position: 0,
      length: oldContent.length,
      text: newContent
    }];
  };

  // Handle cursor position change
  var handleCursorPositionChange = function handleCursorPositionChange(e) {
    if (!editorRef.current) return;
    var cursorPosition = e.target.selectionStart;

    // Only send update if position changed
    if (cursorPosition !== lastCursorPosition.current) {
      lastCursorPosition.current = cursorPosition;

      // Send cursor update
      _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.updateCursor(documentId, cursorPosition);
    }
  };

  // Handle selection change
  var handleSelectionChange = function handleSelectionChange(e) {
    var _lastSelectionRange$c, _lastSelectionRange$c2;
    if (!editorRef.current) return;
    var selectionStart = e.target.selectionStart;
    var selectionEnd = e.target.selectionEnd;

    // Only send update if selection changed
    if (selectionStart !== ((_lastSelectionRange$c = lastSelectionRange.current) === null || _lastSelectionRange$c === void 0 ? void 0 : _lastSelectionRange$c.start) || selectionEnd !== ((_lastSelectionRange$c2 = lastSelectionRange.current) === null || _lastSelectionRange$c2 === void 0 ? void 0 : _lastSelectionRange$c2.end)) {
      lastSelectionRange.current = {
        start: selectionStart,
        end: selectionEnd
      };

      // Only send if there's an actual selection
      if (selectionStart !== selectionEnd) {
        // Send selection update
        _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.updateSelection(documentId, {
          start: selectionStart,
          end: selectionEnd
        });
      }
    }
  };

  // Request sync
  var requestSync = function requestSync() {
    setIsSyncing(true);

    // Send sync request
    _services_SharedEditingService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.sendDocumentMessage({
      subtype: 'document_sync_request',
      documentId: documentId
    });
  };

  // Get collaborator count
  var getCollaboratorCount = function getCollaboratorCount() {
    return Object.keys(collaborators).length;
  };

  // Render collaborator list
  var renderCollaborators = function renderCollaborators() {
    var collaboratorList = Object.values(collaborators);
    if (collaboratorList.length === 0) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
        type: "secondary"
      }, "No collaborators");
    }
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, {
      wrap: true
    }, collaboratorList.map(function (collaborator) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_UserPresenceIndicator__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .A, {
        key: collaborator.userId,
        userId: collaborator.userId,
        username: collaborator.username,
        showUsername: false,
        size: "small",
        avatarSize: 24
      });
    }));
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Card */ .Zp, {
    title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .EditOutlined */ .xjh, {
      style: {
        marginRight: 8
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, title), isSyncing && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .SyncOutlined */ .OmY, {
      spin: true,
      style: {
        marginLeft: 8
      }
    })),
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tooltip */ .m_, {
      title: "Collaborators"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Badge */ .Ex, {
      count: getCollaboratorCount(),
      size: "small"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .TeamOutlined */ .QM0, null),
      size: "small",
      type: "text"
    }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tooltip */ .m_, {
      title: "Sync Document"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .SyncOutlined */ .OmY, {
        spin: isSyncing
      }),
      size: "small",
      onClick: requestSync,
      disabled: isSyncing
    }))),
    style: _objectSpread({}, style)
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      marginBottom: 8
    }
  }, renderCollaborators()), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    "data-testid": "quill-editor"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement((react_quill__WEBPACK_IMPORTED_MODULE_4___default()), {
    ref: editorRef,
    value: content,
    onChange: handleContentChange,
    readOnly: readOnly || !isJoined,
    style: {
      height: height
    },
    theme: "snow",
    "data-testid": "quill-content",
    modules: {
      toolbar: [['bold', 'italic', 'underline', 'strike'], ['blockquote', 'code-block'], [{
        'header': 1
      }, {
        'header': 2
      }], [{
        'list': 'ordered'
      }, {
        'list': 'bullet'
      }], [{
        'script': 'sub'
      }, {
        'script': 'super'
      }], [{
        'indent': '-1'
      }, {
        'indent': '+1'
      }], [{
        'color': []
      }, {
        'background': []
      }], ['clean']]
    }
  })), readOnly && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      marginTop: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .EyeOutlined */ .Om2, null), " Read-only mode")));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SharedEditor);

/***/ })

}]);
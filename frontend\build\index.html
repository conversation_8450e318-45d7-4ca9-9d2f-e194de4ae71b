<!doctype html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><meta name="theme-color" content="#2563EB"><meta name="description" content="App Builder 201 - Build your application with minimal setup"><title>App Builder 201</title><link rel="icon" href="/favicon.ico" type="image/x-icon"><link rel="shortcut icon" href="/favicon.ico" type="image/x-icon"><link rel="apple-touch-icon" href="/logo192.png"><link rel="manifest" href="/manifest.json"><link rel="preload" href="/static/css/main.css" as="style"><link rel="stylesheet" href="/static/css/main.css"><style>/* Simple loading animation styles */
    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 5px solid #f3f3f3;
      border-top: 5px solid #2563EB;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }</style><script defer="defer" src="/static/js/runtime.3402d525.js"></script><script defer="defer" src="/static/js/critical-vendor.2594363e.db3a9b5b.js"></script><script defer="defer" src="/static/js/critical-vendor.e5bca7e4.ab379510.js"></script><script defer="defer" src="/static/js/5108.249a5cb5.js"></script><script defer="defer" src="/static/js/9031.59d691a5.js"></script><script defer="defer" src="/static/js/8980.b8d5dedf.js"></script><script defer="defer" src="/static/js/6434.4309534a.js"></script><script defer="defer" src="/static/js/5101.722c18f4.js"></script><script defer="defer" src="/static/js/1025.861fb629.js"></script><script defer="defer" src="/static/js/9156.4c002362.js"></script><script defer="defer" src="/static/js/276.099b688a.js"></script><script defer="defer" src="/static/js/720.adb04a4e.js"></script><script defer="defer" src="/static/js/5435.7dc88345.js"></script><script defer="defer" src="/static/js/1114.466accb8.js"></script><script defer="defer" src="/static/js/7571.0a268386.js"></script><script defer="defer" src="/static/js/2518.8a1201e0.js"></script><script defer="defer" src="/static/js/9060.93936eee.js"></script><script defer="defer" src="/static/js/4772.f9e2566b.js"></script><script defer="defer" src="/static/js/7449.df26c52a.js"></script><script defer="defer" src="/static/js/5141.d9a09229.js"></script><script defer="defer" src="/static/js/9832.729127e0.js"></script><script defer="defer" src="/static/js/2698.c431886f.js"></script><script defer="defer" src="/static/js/2743.c7d181ff.js"></script><script defer="defer" src="/static/js/1629.cbd3aa40.js"></script><script defer="defer" src="/static/js/8287.87410f71.js"></script><script defer="defer" src="/static/js/5000.af666a2d.js"></script><script defer="defer" src="/static/js/1789.59fa2f2e.js"></script><script defer="defer" src="/static/js/7192.7ca05dc8.js"></script><script defer="defer" src="/static/js/9372.3d23e4c8.js"></script><script defer="defer" src="/static/js/4802.63c10a3f.js"></script><script defer="defer" src="/static/js/1807.03f606af.js"></script><script defer="defer" src="/static/js/747.a8e5fd4e.js"></script><script defer="defer" src="/static/js/7088.d8b95dc1.js"></script><script defer="defer" src="/static/js/8278.9af8fc30.js"></script><script defer="defer" src="/static/js/6059.1bd3bcb2.js"></script><script defer="defer" src="/static/js/8346.3e1a42f0.js"></script><script defer="defer" src="/static/js/687.b171d465.js"></script><script defer="defer" src="/static/js/2036.89f8d3c4.js"></script><script defer="defer" src="/static/js/4488.894a11ec.js"></script><script defer="defer" src="/static/js/7496.867acca7.js"></script><script defer="defer" src="/static/js/9681.c02945db.js"></script><script defer="defer" src="/static/js/2773.31fe48be.js"></script><script defer="defer" src="/static/js/6110.d5ead66c.js"></script><script defer="defer" src="/static/js/6037.1be056d4.js"></script><script defer="defer" src="/static/js/3357.fec03e24.js"></script><script defer="defer" src="/static/js/2272.6cea77bf.js"></script><script defer="defer" src="/static/js/895.f042adc5.js"></script><script defer="defer" src="/static/js/1115.db271327.js"></script><script defer="defer" src="/static/js/3205.1a70db9c.js"></script><script defer="defer" src="/static/js/1540.7ed60e4b.js"></script><script defer="defer" src="/static/js/6261.be21bebf.js"></script><script defer="defer" src="/static/js/9210.be8363af.js"></script><script defer="defer" src="/static/js/8104.3c9c8155.js"></script><script defer="defer" src="/static/js/9225.820467f5.js"></script><script defer="defer" src="/static/js/6438.b1c99aca.js"></script><script defer="defer" src="/static/js/6248.ba805236.js"></script><script defer="defer" src="/static/js/2378.88876f37.js"></script><script defer="defer" src="/static/js/8954.a6918c33.js"></script><script defer="defer" src="/static/js/14.e4b015c0.js"></script><script defer="defer" src="/static/js/38.d8d93798.js"></script><script defer="defer" src="/static/js/5588.beabfb51.js"></script><script defer="defer" src="/static/js/1387.c2876090.js"></script><script defer="defer" src="/static/js/5955.19fe0622.js"></script><script defer="defer" src="/static/js/7419.7cf85034.js"></script><script defer="defer" src="/static/js/main.9931d6b7.d0b50bd2.js"></script><script defer="defer" src="/static/js/main.84781932.6b478a0b.js"></script><link href="/static/css/main.9d84abd5.4099fbde.css" rel="stylesheet"></head><body><noscript>You need to enable JavaScript to run this app.</noscript><div id="root"><div class="loading-container"><div class="loading-spinner"></div><h2>Loading App Builder 201...</h2><p>Preparing your development environment</p></div></div><script>if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/service-worker.js')
          .then(registration => {
            console.log('Service Worker registered with scope:', registration.scope);

            // Check for updates
            registration.addEventListener('updatefound', () => {
              const newWorker = registration.installing;
              console.log('Service Worker update found!');

              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  // New content is available, show notification
                  if (window.confirm('New version available! Reload to update?')) {
                    window.location.reload();
                  }
                }
              });
            });
          })
          .catch(error => {
            console.error('Service Worker registration failed:', error);
          });

        // Handle service worker updates
        let refreshing = false;
        navigator.serviceWorker.addEventListener('controllerchange', () => {
          if (!refreshing) {
            refreshing = true;
            window.location.reload();
          }
        });
      });
    }</script></body></html>
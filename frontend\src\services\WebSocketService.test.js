import webSocketService from './WebSocketService';
import { setupWebSocketMock } from '../tests/mocks/websocket.mock';

describe('WebSocketService', () => {
  let cleanupWebSocketMock;

  beforeEach(() => {
    // Setup WebSocket mock
    cleanupWebSocketMock = setupWebSocketMock();

    // Reset WebSocketService state
    webSocketService.socket = null;
    webSocketService.connected = false;
    webSocketService.reconnectAttempts = 0;
    webSocketService.listeners = {};
    webSocketService.messageTypeListeners = {};
  });

  afterEach(() => {
    // Clean up WebSocket mock
    if (cleanupWebSocketMock) {
      cleanupWebSocketMock();
    }

    // Disconnect if connected
    if (webSocketService.socket) {
      webSocketService.disconnect();
    }
  });

  test('should connect to WebSocket', (done) => {
    // Add a listener for the connect event
    webSocketService.addListener('connect', () => {
      expect(webSocketService.isConnected()).toBe(true);
      done();
    });

    // Connect to WebSocket
    webSocketService.connect();
  });

  test('should disconnect from WebSocket', (done) => {
    // Connect to WebSocket
    webSocketService.connect();

    // Add a listener for the disconnect event
    webSocketService.addListener('disconnect', () => {
      expect(webSocketService.isConnected()).toBe(false);
      done();
    });

    // Wait for the connection to be established
    setTimeout(() => {
      // Disconnect from WebSocket
      webSocketService.disconnect();
    }, 20);
  });

  test('should send messages to WebSocket', (done) => {
    // Connect to WebSocket
    webSocketService.connect();

    // Wait for the connection to be established
    setTimeout(() => {
      // Mock the send method
      const originalSend = webSocketService.socket.send;
      webSocketService.socket.send = jest.fn(originalSend);

      // Send a message
      const message = { type: 'test', data: 'test data' };
      webSocketService.send(message);

      // Check that the send method was called with the correct data
      expect(webSocketService.socket.send).toHaveBeenCalledWith(JSON.stringify(message));

      done();
    }, 20);
  });

  test('should receive messages from WebSocket', (done) => {
    // Connect to WebSocket
    webSocketService.connect();

    // Add a listener for a specific message type
    const messageType = 'test';
    const messageData = { type: messageType, data: 'test data' };

    webSocketService.addMessageTypeListener(messageType, (data) => {
      expect(data).toEqual(messageData);
      done();
    });

    // Wait for the connection to be established
    setTimeout(() => {
      // Simulate receiving a message
      webSocketService.socket.onmessage({ data: JSON.stringify(messageData) });
    }, 20);
  });

  test('should handle reconnection', (done) => {
    // Set the reconnect delay to a small value for testing
    const originalReconnectDelay = webSocketService.reconnectDelay;
    webSocketService.reconnectDelay = 10;

    // Connect to WebSocket
    webSocketService.connect();

    // Add a listener for the connect event
    let connectCount = 0;
    webSocketService.addListener('connect', () => {
      connectCount++;

      if (connectCount === 1) {
        // Simulate a connection error after the first connection
        webSocketService.socket.onclose({ code: 1006, reason: 'Connection error' });
      } else if (connectCount === 2) {
        // The second connection is the reconnection
        expect(webSocketService.reconnectAttempts).toBe(1);

        // Restore the original reconnect delay
        webSocketService.reconnectDelay = originalReconnectDelay;

        done();
      }
    });
  });

  test('should handle multiple listeners', (done) => {
    // Connect to WebSocket
    webSocketService.connect();

    // Add multiple listeners for the same event
    const messageType = 'test';
    const messageData = { type: messageType, data: 'test data' };

    let listenerCount = 0;

    webSocketService.addMessageTypeListener(messageType, (data) => {
      expect(data).toEqual(messageData);
      listenerCount++;
      if (listenerCount === 2) done();
    });

    webSocketService.addMessageTypeListener(messageType, (data) => {
      expect(data).toEqual(messageData);
      listenerCount++;
      if (listenerCount === 2) done();
    });

    // Wait for the connection to be established
    setTimeout(() => {
      // Simulate receiving a message
      webSocketService.socket.onmessage({ data: JSON.stringify(messageData) });
    }, 20);
  });
});

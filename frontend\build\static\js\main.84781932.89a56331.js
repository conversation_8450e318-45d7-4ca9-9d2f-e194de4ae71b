"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[9682],{

/***/ 4318:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AS: () => (/* binding */ WS_ERROR),
/* harmony export */   D: () => (/* binding */ REMOVE_THEME),
/* harmony export */   H2: () => (/* binding */ WS_MESSAGE),
/* harmony export */   Kg: () => (/* binding */ WEBSOCKET_CONNECTED),
/* harmony export */   Pe: () => (/* binding */ UPDATE_LAYOUT),
/* harmony export */   RH: () => (/* binding */ WS_DISCONNECTED),
/* harmony export */   Te: () => (/* binding */ WS_CONNECTED),
/* harmony export */   U_: () => (/* binding */ ADD_THEME),
/* harmony export */   WD: () => (/* binding */ WS_SEND_MESSAGE),
/* harmony export */   YG: () => (/* binding */ WS_CONNECT),
/* harmony export */   ZH: () => (/* binding */ WS_MESSAGE_RECEIVED),
/* harmony export */   _E: () => (/* binding */ TOGGLE_AUTO_APPLY_THEME),
/* harmony export */   co: () => (/* binding */ WEBSOCKET_DISCONNECTED),
/* harmony export */   ei: () => (/* binding */ UPDATE_COMPONENT),
/* harmony export */   gV: () => (/* binding */ REMOVE_LAYOUT),
/* harmony export */   gk: () => (/* binding */ UPDATE_THEME),
/* harmony export */   oz: () => (/* binding */ ADD_COMPONENT),
/* harmony export */   uV: () => (/* binding */ WS_DISCONNECT),
/* harmony export */   vs: () => (/* binding */ ADD_LAYOUT),
/* harmony export */   wH: () => (/* binding */ SET_ACTIVE_THEME),
/* harmony export */   xS: () => (/* binding */ REMOVE_COMPONENT)
/* harmony export */ });
/* unused harmony exports WEBSOCKET_ERROR, WS_RECONNECT, WS_CLEAR_MESSAGES, FETCH_APP_DATA_REQUEST, FETCH_APP_DATA_SUCCESS, FETCH_APP_DATA_FAILURE, SAVE_APP_DATA_REQUEST, SAVE_APP_DATA_SUCCESS, SAVE_APP_DATA_FAILURE, ADD_STYLE, UPDATE_STYLE, REMOVE_STYLE, ADD_DATA, UPDATE_DATA, REMOVE_DATA, SAVE_USER_THEME_PREFERENCE, FETCH_API_KEYS_REQUEST, FETCH_API_KEYS_SUCCESS, FETCH_API_KEYS_FAILURE, VALIDATE_API_KEY_REQUEST, VALIDATE_API_KEY_SUCCESS, VALIDATE_API_KEY_FAILURE, GENERATE_AI_SUGGESTIONS_REQUEST, GENERATE_AI_SUGGESTIONS_SUCCESS, GENERATE_AI_SUGGESTIONS_FAILURE, GENERATE_IMAGE_REQUEST, GENERATE_IMAGE_SUCCESS, GENERATE_IMAGE_FAILURE, AI_UNDO, AI_REDO, AI_SAVE_STATE, AI_CLEAR_HISTORY, AI_JUMP_TO_STATE, AI_RESTORE_STATE, APPLY_AI_LAYOUT, APPLY_AI_COMPONENT_COMBINATION, AI_BULK_CHANGES */
// WebSocket action types
var WEBSOCKET_CONNECTED = 'WEBSOCKET_CONNECTED';
var WEBSOCKET_DISCONNECTED = 'WEBSOCKET_DISCONNECTED';
var WEBSOCKET_ERROR = 'WEBSOCKET_ERROR';

// New WebSocket action types
var WS_CONNECT = 'WS_CONNECT';
var WS_CONNECTED = 'WS_CONNECTED';
var WS_DISCONNECT = 'WS_DISCONNECT';
var WS_DISCONNECTED = 'WS_DISCONNECTED';
var WS_MESSAGE = 'WS_MESSAGE';
var WS_MESSAGE_RECEIVED = 'WS_MESSAGE_RECEIVED';
var WS_SEND_MESSAGE = 'WS_SEND_MESSAGE';
var WS_ERROR = 'WS_ERROR';
var WS_RECONNECT = 'WS_RECONNECT';
var WS_CLEAR_MESSAGES = 'WS_CLEAR_MESSAGES';

// App data action types
var FETCH_APP_DATA_REQUEST = 'FETCH_APP_DATA_REQUEST';
var FETCH_APP_DATA_SUCCESS = 'FETCH_APP_DATA_SUCCESS';
var FETCH_APP_DATA_FAILURE = 'FETCH_APP_DATA_FAILURE';
var SAVE_APP_DATA_REQUEST = 'SAVE_APP_DATA_REQUEST';
var SAVE_APP_DATA_SUCCESS = 'SAVE_APP_DATA_SUCCESS';
var SAVE_APP_DATA_FAILURE = 'SAVE_APP_DATA_FAILURE';

// Component action types
var ADD_COMPONENT = 'ADD_COMPONENT';
var UPDATE_COMPONENT = 'UPDATE_COMPONENT';
var REMOVE_COMPONENT = 'REMOVE_COMPONENT';

// Layout action types
var ADD_LAYOUT = 'ADD_LAYOUT';
var UPDATE_LAYOUT = 'UPDATE_LAYOUT';
var REMOVE_LAYOUT = 'REMOVE_LAYOUT';

// Style action types
var ADD_STYLE = 'ADD_STYLE';
var UPDATE_STYLE = 'UPDATE_STYLE';
var REMOVE_STYLE = 'REMOVE_STYLE';

// Data action types
var ADD_DATA = 'ADD_DATA';
var UPDATE_DATA = 'UPDATE_DATA';
var REMOVE_DATA = 'REMOVE_DATA';

// Theme action types
var ADD_THEME = 'ADD_THEME';
var UPDATE_THEME = 'UPDATE_THEME';
var REMOVE_THEME = 'REMOVE_THEME';
var SET_ACTIVE_THEME = 'SET_ACTIVE_THEME';
var SAVE_USER_THEME_PREFERENCE = 'SAVE_USER_THEME_PREFERENCE';
var TOGGLE_AUTO_APPLY_THEME = 'TOGGLE_AUTO_APPLY_THEME';

// API Keys action types
var FETCH_API_KEYS_REQUEST = 'FETCH_API_KEYS_REQUEST';
var FETCH_API_KEYS_SUCCESS = 'FETCH_API_KEYS_SUCCESS';
var FETCH_API_KEYS_FAILURE = 'FETCH_API_KEYS_FAILURE';
var VALIDATE_API_KEY_REQUEST = 'VALIDATE_API_KEY_REQUEST';
var VALIDATE_API_KEY_SUCCESS = 'VALIDATE_API_KEY_SUCCESS';
var VALIDATE_API_KEY_FAILURE = 'VALIDATE_API_KEY_FAILURE';

// AI action types
var GENERATE_AI_SUGGESTIONS_REQUEST = 'GENERATE_AI_SUGGESTIONS_REQUEST';
var GENERATE_AI_SUGGESTIONS_SUCCESS = 'GENERATE_AI_SUGGESTIONS_SUCCESS';
var GENERATE_AI_SUGGESTIONS_FAILURE = 'GENERATE_AI_SUGGESTIONS_FAILURE';
var GENERATE_IMAGE_REQUEST = 'GENERATE_IMAGE_REQUEST';
var GENERATE_IMAGE_SUCCESS = 'GENERATE_IMAGE_SUCCESS';
var GENERATE_IMAGE_FAILURE = 'GENERATE_IMAGE_FAILURE';

// AI Undo/Redo action types
var AI_UNDO = 'AI_UNDO';
var AI_REDO = 'AI_REDO';
var AI_SAVE_STATE = 'AI_SAVE_STATE';
var AI_CLEAR_HISTORY = 'AI_CLEAR_HISTORY';
var AI_JUMP_TO_STATE = 'AI_JUMP_TO_STATE';
var AI_RESTORE_STATE = 'AI_RESTORE_STATE';

// AI Layout and Component action types
var APPLY_AI_LAYOUT = 'APPLY_AI_LAYOUT';
var APPLY_AI_COMPONENT_COMBINATION = 'APPLY_AI_COMPONENT_COMBINATION';
var AI_BULK_CHANGES = 'AI_BULK_CHANGES';

/***/ }),

/***/ 11606:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(23029);
/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(92901);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(84447);




/**
 * Authentication Service
 *
 * This service handles user authentication, including login, logout, and token management.
 */



// Constants
var TOKEN_KEY = 'app_builder_auth_token';
var USER_KEY = 'app_builder_user';
var DEV_MODE = "production" === 'development';

/**
 * Authentication Service
 */
var AuthService = /*#__PURE__*/function () {
  function AuthService() {
    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(this, AuthService);
    this.token = localStorage.getItem(TOKEN_KEY);
    this.user = JSON.parse(localStorage.getItem(USER_KEY) || 'null');
    this.listeners = [];

    // In development mode, create a default user if none exists
    if (DEV_MODE && !this.token) {
      console.log('Development mode: Creating default authentication');
      this.token = 'dev-token-for-testing';
      this.user = {
        id: 1,
        name: 'Development User',
        email: '<EMAIL>',
        roles: ['user', 'admin']
      };

      // Store in localStorage
      localStorage.setItem(TOKEN_KEY, this.token);
      localStorage.setItem(USER_KEY, JSON.stringify(this.user));
    }

    // Initialize axios interceptors
    this.initInterceptors();
  }

  /**
   * Initialize axios interceptors
   */
  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(AuthService, [{
    key: "initInterceptors",
    value: function initInterceptors() {
      var _this = this;
      // Add token to requests
      axios__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay.interceptors.request.use(function (config) {
        if (_this.token) {
          config.headers.Authorization = "Bearer ".concat(_this.token);
        }
        return config;
      }, function (error) {
        return Promise.reject(error);
      });

      // Handle 401 responses
      axios__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay.interceptors.response.use(function (response) {
        return response;
      }, function (error) {
        if (error.response && error.response.status === 401) {
          _this.logout();
        }
        return Promise.reject(error);
      });
    }

    /**
     * Login with email and password
     * @param {string} email - User email
     * @param {string} password - User password
     * @returns {Promise<Object>} User data
     */
  }, {
    key: "login",
    value: (function () {
      var _login = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee(email, password) {
        var response, _response$data, token, user, _t;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              _context.next = 1;
              return axios__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay.post('/api/auth/login', {
                email: email,
                password: password
              });
            case 1:
              response = _context.sent;
              _response$data = response.data, token = _response$data.token, user = _response$data.user;
              this.setToken(token);
              this.setUser(user);
              this.notifyListeners('login', user);
              return _context.abrupt("return", user);
            case 2:
              _context.prev = 2;
              _t = _context["catch"](0);
              console.error('Login error:', _t);
              throw _t;
            case 3:
            case "end":
              return _context.stop();
          }
        }, _callee, this, [[0, 2]]);
      }));
      function login(_x, _x2) {
        return _login.apply(this, arguments);
      }
      return login;
    }()
    /**
     * Register a new user
     * @param {string} name - User name
     * @param {string} email - User email
     * @param {string} password - User password
     * @returns {Promise<Object>} User data
     */
    )
  }, {
    key: "register",
    value: (function () {
      var _register = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee2(name, email, password) {
        var response, _response$data2, token, user, _t2;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              _context2.prev = 0;
              _context2.next = 1;
              return axios__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay.post('/api/auth/register', {
                name: name,
                email: email,
                password: password
              });
            case 1:
              response = _context2.sent;
              _response$data2 = response.data, token = _response$data2.token, user = _response$data2.user;
              this.setToken(token);
              this.setUser(user);
              this.notifyListeners('register', user);
              return _context2.abrupt("return", user);
            case 2:
              _context2.prev = 2;
              _t2 = _context2["catch"](0);
              console.error('Register error:', _t2);
              throw _t2;
            case 3:
            case "end":
              return _context2.stop();
          }
        }, _callee2, this, [[0, 2]]);
      }));
      function register(_x3, _x4, _x5) {
        return _register.apply(this, arguments);
      }
      return register;
    }()
    /**
     * Logout the current user
     */
    )
  }, {
    key: "logout",
    value: function logout() {
      this.setToken(null);
      this.setUser(null);
      this.notifyListeners('logout');
    }

    /**
     * Get the current user
     * @returns {Object|null} User data
     */
  }, {
    key: "getUser",
    value: function getUser() {
      return this.user;
    }

    /**
     * Check if the user is authenticated
     * @returns {boolean} True if the user is authenticated
     */
  }, {
    key: "isAuthenticated",
    value: function isAuthenticated() {
      return !!this.token;
    }

    /**
     * Set the authentication token
     * @param {string|null} token - Authentication token
     */
  }, {
    key: "setToken",
    value: function setToken(token) {
      this.token = token;
      if (token) {
        localStorage.setItem(TOKEN_KEY, token);
      } else {
        localStorage.removeItem(TOKEN_KEY);
      }
    }

    /**
     * Set the current user
     * @param {Object|null} user - User data
     */
  }, {
    key: "setUser",
    value: function setUser(user) {
      this.user = user;
      if (user) {
        localStorage.setItem(USER_KEY, JSON.stringify(user));
      } else {
        localStorage.removeItem(USER_KEY);
      }
    }

    /**
     * Add a listener for authentication events
     * @param {Function} listener - Listener function
     * @returns {Function} Function to remove the listener
     */
  }, {
    key: "addListener",
    value: function addListener(listener) {
      var _this2 = this;
      this.listeners.push(listener);
      return function () {
        _this2.listeners = _this2.listeners.filter(function (l) {
          return l !== listener;
        });
      };
    }

    /**
     * Notify all listeners of an authentication event
     * @param {string} event - Event name
     * @param {*} data - Event data
     */
  }, {
    key: "notifyListeners",
    value: function notifyListeners(event, data) {
      this.listeners.forEach(function (listener) {
        try {
          listener(event, data);
        } catch (error) {
          console.error('Error in auth listener:', error);
        }
      });
    }
  }]);
}(); // Create a singleton instance
var authService = new AuthService();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (authService);

/***/ }),

/***/ 17053:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(82284);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(23029);
/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(92901);





function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
// Singleton WebSocket service with safe initialization
var WebSocketService = /*#__PURE__*/function () {
  function WebSocketService() {
    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(this, WebSocketService);
    this.socket = null;
    this.connected = false;
    this.connecting = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
    this.reconnectInterval = 2000;
    this.maxReconnectInterval = 30000;
    this.reconnectDecay = 1.5;
    this.eventListeners = {};
    this.offlineQueue = [];
    this.isReconnecting = false;
    this.isClosing = false;
    this.isSuspended = false;
    this.messageQueue = [];
    this.lastError = null;
    this.heartbeatInterval = 30000; // 30 seconds
    this.heartbeatTimeoutId = null;
    this.missedHeartbeats = 0;
    this.maxMissedHeartbeats = 3;
    this.connectionTimeoutId = null;
    this.debug = false;
    this.instance = null;
  }

  // Initialize with safe defaults
  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(WebSocketService, [{
    key: "init",
    value: function init() {
      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      this.options = _objectSpread({
        autoConnect: false,
        autoReconnect: true,
        reconnectInterval: 2000,
        maxReconnectAttempts: 10,
        debug: false,
        queueOfflineMessages: true
      }, options);

      // Set safe defaults
      this.connected = false;
      this.connecting = false;
      this.debug = this.options.debug;
      if (this.options.autoConnect) {
        this.connect();
      }
      return this;
    }

    // Set reconnect options
  }, {
    key: "setReconnectOptions",
    value: function setReconnectOptions() {
      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      this.maxReconnectAttempts = options.maxAttempts || this.maxReconnectAttempts;
      this.reconnectInterval = options.initialDelay || this.reconnectInterval;
      this.maxReconnectInterval = options.maxDelay || this.maxReconnectInterval;
      this.reconnectDecay = options.useExponentialBackoff ? 1.5 : 1;
      return this;
    }

    // Configure security options
  }, {
    key: "configureSecurityOptions",
    value: function configureSecurityOptions() {
      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      this.securityOptions = _objectSpread({
        validateMessages: true,
        sanitizeMessages: true,
        rateLimiting: {
          enabled: true,
          maxMessagesPerSecond: 20,
          burstSize: 50
        }
      }, options);
      return this;
    }

    // Configure performance options
  }, {
    key: "configurePerformanceOptions",
    value: function configurePerformanceOptions() {
      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      this.performanceOptions = _objectSpread({
        compression: {
          enabled: true,
          threshold: 1024,
          level: 6
        },
        batchingEnabled: true,
        batchInterval: 50,
        maxBatchSize: 20,
        offlineQueueEnabled: true,
        offlineStorage: {
          enabled: true,
          persistKey: 'websocket_offline_queue'
        }
      }, options);
      return this;
    }

    // Safe connection check that never throws
  }, {
    key: "isConnected",
    value: function isConnected() {
      try {
        return this.socket !== null && this.socket !== undefined && this.socket.readyState === WebSocket.OPEN;
      } catch (error) {
        console.error('Error checking connection status:', error);
        return false;
      }
    }

    // Connect to WebSocket server
  }, {
    key: "connect",
    value: function connect(endpoint) {
      var _this = this;
      return new Promise(function (resolve, reject) {
        if (_this.isConnected()) {
          resolve(_this);
          return;
        }
        if (_this.connecting) {
          reject(new Error('Connection already in progress'));
          return;
        }
        _this.connecting = true;

        // Determine WebSocket URL
        var url;
        if (endpoint) {
          _this.endpoint = endpoint;
        }

        // Use current location if no URL is specified
        if (!_this.url) {
          // Check if we're in development mode
          var isDev = "production" === 'development';

          // Use our simple WebSocket server with fallbacks
          if (isDev) {
            // Try multiple URLs for development
            var possibleUrls = ['ws://localhost:8000/ws', 'ws://127.0.0.1:8000/ws', 'ws://localhost:8765', 'ws://backend:8000/ws'];

            // Use the first URL as default
            url = possibleUrls[0];

            // Store fallback URLs
            _this.fallbackUrls = possibleUrls.slice(1);
            if (_this.debug) {
              console.log('Development mode: Using WebSocket server at:', url);
              console.log('Fallback URLs:', _this.fallbackUrls);
            }
          } else {
            // Production default
            url = 'ws://localhost:8765';
          }
        } else {
          url = _this.url;
        }
        if (_this.debug) {
          console.log("Connecting to WebSocket at ".concat(url));
        }
        try {
          _this.socket = new WebSocket(url);
          _this.socket.onopen = function (event) {
            _this.connected = true;
            _this.connecting = false;
            _this.reconnectAttempts = 0;
            if (_this.debug) {
              console.log('WebSocket connection established');
            }

            // Start heartbeat
            _this.startHeartbeat();

            // Process any queued messages
            _this.processOfflineQueue();

            // Trigger event listeners
            _this.triggerEvent('open', event);
            _this.triggerEvent('connect', event);
            resolve(_this);
          };
          _this.socket.onclose = function (event) {
            _this.connected = false;
            _this.connecting = false;
            if (_this.debug) {
              console.log("WebSocket connection closed: ".concat(event.code, " ").concat(event.reason));
            }

            // Stop heartbeat
            _this.stopHeartbeat();

            // Trigger event listeners
            _this.triggerEvent('close', event);
            _this.triggerEvent('disconnect', event);

            // Attempt to reconnect if not explicitly closed and auto reconnect is enabled
            if (!_this.isClosing && _this.options.autoReconnect) {
              _this.reconnect();
            }
            _this.isClosing = false;
          };
          _this.socket.onerror = function (event) {
            // Log detailed error information
            console.error('WebSocket error occurred:', event);
            if (_this.debug) {
              console.log('WebSocket readyState:', _this.socket.readyState);
              console.log('Connection URL:', url);
              console.log('Browser:', navigator.userAgent);
            }

            // Handle the error with recovery strategies
            var error = _this._handleConnectionError(event, _this.reconnectAttempts);

            // If still connecting, reject the promise
            if (_this.connecting) {
              _this.connecting = false;
              reject(error);
            }

            // Trigger error event with detailed information
            _this.triggerEvent('error', {
              originalEvent: event,
              url: url,
              timestamp: Date.now(),
              reconnectAttempts: _this.reconnectAttempts,
              error: error
            });
          };
          _this.socket.onmessage = function (event) {
            var data = event.data;
            try {
              // Check if message is compressed
              if (typeof data === 'string' && data.startsWith('C')) {
                // Remove compression header
                var compressedData = data.substring(1);

                // Decompress the data
                data = _this._decompressMessage(compressedData);
              }

              // Parse JSON data
              if (typeof data === 'string') {
                data = JSON.parse(data);
              }
              if (_this.debug) {
                console.log('WebSocket message received:', data);
              }

              // Handle heartbeat response
              if (data.type === 'pong') {
                _this.missedHeartbeats = 0;
                _this.triggerEvent('pong', data);
                return;
              }

              // Handle batch messages
              if (data.type === 'batch' && Array.isArray(data.messages)) {
                // Process each message in the batch
                data.messages.forEach(function (message) {
                  _this.triggerEvent('message', message);

                  // Trigger specific event type if available
                  if (message.type) {
                    _this.triggerEvent(message.type, message);
                  }
                });

                // Trigger batch event
                _this.triggerEvent('batch', data);
                return;
              }

              // Trigger event listeners
              _this.triggerEvent('message', data);

              // Trigger specific event type if available
              if (data.type) {
                _this.triggerEvent(data.type, data);
              }
            } catch (error) {
              // Handle message processing error
              _this._handleMessageError(error, event.data);
            }
          };
        } catch (error) {
          _this.connecting = false;
          _this.lastError = error;
          if (_this.debug) {
            console.error('Error creating WebSocket:', error);
          }
          reject(error);
        }
      });
    }

    // Disconnect from WebSocket server
  }, {
    key: "disconnect",
    value: function disconnect() {
      if (!this.socket) {
        return;
      }
      this.isClosing = true;
      try {
        this.stopHeartbeat();
        this.socket.close(1000, 'Normal closure');
        if (this.debug) {
          console.log('WebSocket disconnected');
        }
      } catch (error) {
        console.error('Error disconnecting WebSocket:', error);
      }
    }

    // Alias for disconnect
  }, {
    key: "close",
    value: function close() {
      return this.disconnect();
    }

    // Reconnect to WebSocket server
  }, {
    key: "reconnect",
    value: function reconnect() {
      var _this2 = this;
      if (this.isReconnecting) {
        return;
      }
      this.isReconnecting = true;
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        if (this.debug) {
          console.log("Maximum reconnect attempts (".concat(this.maxReconnectAttempts, ") reached"));
        }
        this.triggerEvent('max_retries_reached', {
          attempts: this.reconnectAttempts,
          max: this.maxReconnectAttempts
        });
        this.isReconnecting = false;
        return;
      }
      this.reconnectAttempts++;

      // Calculate reconnect delay with exponential backoff and enhanced jitter
      var exponentialDelay = this.reconnectInterval * Math.pow(this.reconnectDecay, this.reconnectAttempts - 1);
      exponentialDelay = Math.min(exponentialDelay, this.maxReconnectInterval);

      // Enhanced jitter implementation to prevent thundering herd problem
      // Use ±30% jitter with better distribution
      var jitterFactor = 0.3;
      var jitterRange = exponentialDelay * jitterFactor;
      var jitter = (Math.random() - 0.5) * 2 * jitterRange;

      // Ensure delay is positive and within reasonable bounds
      var delay = Math.max(this.reconnectInterval, Math.round(exponentialDelay + jitter));
      if (this.debug) {
        console.log("Reconnecting in ".concat(Math.round(delay / 1000), "s (attempt ").concat(this.reconnectAttempts, "/").concat(this.maxReconnectAttempts, ")"), {
          exponentialDelay: Math.round(exponentialDelay),
          jitter: Math.round(jitter),
          finalDelay: delay,
          jitterFactor: jitterFactor
        });
      }
      this.triggerEvent('reconnecting', {
        attempt: this.reconnectAttempts,
        maxAttempts: this.maxReconnectAttempts,
        delay: delay
      });
      setTimeout(function () {
        _this2.isReconnecting = false;
        _this2.connect()["catch"](function (error) {
          console.error('Reconnection failed:', error);
        });
      }, delay);
    }

    // Suspend WebSocket connection (e.g., when page is hidden)
  }, {
    key: "suspend",
    value: function suspend() {
      if (this.isSuspended) {
        return;
      }
      this.isSuspended = true;
      if (this.debug) {
        console.log('WebSocket connection suspended');
      }

      // Store connection state before suspending
      this.wasPreviouslyConnected = this.isConnected();

      // Disconnect but don't trigger reconnect
      if (this.socket) {
        this.isClosing = true;
        this.stopHeartbeat();
        this.socket.close(1000, 'Connection suspended');
      }
      this.triggerEvent('suspended', {
        timestamp: Date.now(),
        wasPreviouslyConnected: this.wasPreviouslyConnected
      });
      return this;
    }

    // Resume WebSocket connection (e.g., when page becomes visible again)
  }, {
    key: "resume",
    value: function resume() {
      if (!this.isSuspended) {
        return;
      }
      this.isSuspended = false;
      if (this.debug) {
        console.log('Resuming WebSocket connection');
      }

      // Reconnect if previously connected
      if (this.wasPreviouslyConnected) {
        this.reconnect();
      }
      this.triggerEvent('resumed', {
        timestamp: Date.now(),
        reconnecting: this.wasPreviouslyConnected
      });
      return this;
    }

    // Handle online event
  }, {
    key: "handleOnline",
    value: function handleOnline() {
      if (this.debug) {
        console.log('Network is online');
      }

      // Reconnect if previously connected
      if (this.wasPreviouslyConnected) {
        this.reconnect();
      }
      this.triggerEvent('network_status_change', {
        status: 'online',
        timestamp: Date.now()
      });
      return this;
    }

    // Handle offline event
  }, {
    key: "handleOffline",
    value: function handleOffline() {
      if (this.debug) {
        console.log('Network is offline');
      }

      // Store connection state
      this.wasPreviouslyConnected = this.isConnected();
      this.triggerEvent('network_status_change', {
        status: 'offline',
        timestamp: Date.now()
      });
      return this;
    }

    // Send a message to the WebSocket server
  }, {
    key: "send",
    value: function send(message) {
      var _this3 = this;
      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      return new Promise(function (resolve, reject) {
        if (!_this3.isConnected()) {
          if (_this3.options.queueOfflineMessages) {
            _this3.queueMessage(message, options);
            resolve({
              queued: true
            });
          } else {
            reject(new Error('WebSocket is not connected'));
          }
          return;
        }
        try {
          var _this3$securityOption, _this3$securityOption3, _this3$securityOption4, _this3$securityOption5, _this3$performanceOpt, _this3$performanceOpt2;
          // Apply rate limiting if enabled
          if ((_this3$securityOption = _this3.securityOptions) !== null && _this3$securityOption !== void 0 && (_this3$securityOption = _this3$securityOption.rateLimiting) !== null && _this3$securityOption !== void 0 && _this3$securityOption.enabled) {
            var now = Date.now();

            // Initialize rate limiting state if not exists
            if (!_this3._rateLimitState) {
              _this3._rateLimitState = {
                messageCount: 0,
                windowStart: now,
                burstCount: 0,
                burstStart: now,
                queue: []
              };
            }
            var state = _this3._rateLimitState;
            var _this3$securityOption2 = _this3.securityOptions.rateLimiting,
              maxMessagesPerSecond = _this3$securityOption2.maxMessagesPerSecond,
              burstSize = _this3$securityOption2.burstSize;

            // Reset window if needed
            if (now - state.windowStart > 1000) {
              state.messageCount = 0;
              state.windowStart = now;
            }

            // Reset burst if needed
            if (now - state.burstStart > 100) {
              state.burstCount = 0;
              state.burstStart = now;
            }

            // Check if rate limit exceeded
            if (state.messageCount >= maxMessagesPerSecond || state.burstCount >= burstSize) {
              // Queue the message for later sending
              if (options.important) {
                // Important messages go to the front of the queue
                state.queue.unshift({
                  message: message,
                  options: options,
                  timestamp: now
                });
              } else {
                state.queue.push({
                  message: message,
                  options: options,
                  timestamp: now
                });
              }
              _this3.triggerEvent('rate_limited', {
                queueLength: state.queue.length,
                messageCount: state.messageCount,
                burstCount: state.burstCount
              });

              // Schedule processing of the queue
              if (!state.processingQueue) {
                state.processingQueue = true;
                setTimeout(function () {
                  return _this3._processRateLimitQueue();
                }, 100);
              }
              resolve({
                queued: true,
                rateLimited: true
              });
              return;
            }

            // Update rate limiting counters
            state.messageCount++;
            state.burstCount++;
          }

          // Prepare the message
          var data = message;

          // Add timestamp if not present
          if ((0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(data) === 'object' && !data.timestamp) {
            data = _objectSpread(_objectSpread({}, data), {}, {
              timestamp: Date.now()
            });
          }

          // Add message ID if not present
          if ((0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(data) === 'object' && !data.id) {
            data = _objectSpread(_objectSpread({}, data), {}, {
              id: _this3._generateMessageId()
            });
          }

          // Validate message if enabled
          if ((_this3$securityOption3 = _this3.securityOptions) !== null && _this3$securityOption3 !== void 0 && _this3$securityOption3.validateMessages) {
            var validationResult = _this3._validateMessage(data);
            if (!validationResult.valid) {
              reject(new Error("Invalid message: ".concat(validationResult.reason)));
              return;
            }
          }

          // Sanitize message if enabled
          if ((_this3$securityOption4 = _this3.securityOptions) !== null && _this3$securityOption4 !== void 0 && _this3$securityOption4.sanitizeMessages) {
            data = _this3._sanitizeMessage(data);
          }

          // Add authentication token if available
          if ((_this3$securityOption5 = _this3.securityOptions) !== null && _this3$securityOption5 !== void 0 && _this3$securityOption5.authToken) {
            data = _objectSpread(_objectSpread({}, data), {}, {
              auth: _this3.securityOptions.authToken
            });
          }

          // Check if message should be batched
          if ((_this3$performanceOpt = _this3.performanceOptions) !== null && _this3$performanceOpt !== void 0 && _this3$performanceOpt.batchingEnabled && !options.immediate) {
            _this3._addToBatch(data, options);
            resolve({
              queued: true,
              batched: true
            });
            return;
          }

          // Check if message should be compressed
          var messageString;
          var compressed = false;
          if ((_this3$performanceOpt2 = _this3.performanceOptions) !== null && _this3$performanceOpt2 !== void 0 && (_this3$performanceOpt2 = _this3$performanceOpt2.compression) !== null && _this3$performanceOpt2 !== void 0 && _this3$performanceOpt2.enabled && (options.compress || _this3.performanceOptions.compression.enabled)) {
            var jsonString = typeof data === 'string' ? data : JSON.stringify(data);

            // Only compress if message is larger than threshold
            if (jsonString.length > _this3.performanceOptions.compression.threshold) {
              try {
                messageString = _this3._compressMessage(jsonString);
                compressed = true;

                // Add compression header
                messageString = "C".concat(messageString);
              } catch (error) {
                console.error('Error compressing message:', error);
                messageString = jsonString;
              }
            } else {
              messageString = jsonString;
            }
          } else {
            // Convert to JSON string if needed
            messageString = typeof data === 'string' ? data : JSON.stringify(data);
          }

          // Send the message
          _this3.socket.send(messageString);
          if (_this3.debug) {
            console.log("WebSocket message sent".concat(compressed ? ' (compressed)' : '', ":"), data);
          }

          // Trigger event
          _this3.triggerEvent('message_sent', {
            message: data,
            compressed: compressed,
            size: messageString.length
          });
          resolve({
            sent: true,
            compressed: compressed
          });
        } catch (error) {
          console.error('Error sending WebSocket message:', error);
          reject(error);
        }
      });
    }

    // Generate a unique message ID
  }, {
    key: "_generateMessageId",
    value: function _generateMessageId() {
      return "".concat(Date.now(), "-").concat(Math.random().toString(36).substring(2, 11));
    }

    // Validate a message
  }, {
    key: "_validateMessage",
    value: function _validateMessage(message) {
      // Basic validation
      if (!message) {
        return {
          valid: false,
          reason: 'Message is empty'
        };
      }
      if ((0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(message) !== 'object') {
        return {
          valid: false,
          reason: 'Message must be an object'
        };
      }
      if (!message.type) {
        return {
          valid: false,
          reason: 'Message must have a type'
        };
      }

      // All checks passed
      return {
        valid: true
      };
    }

    // Sanitize a message
  }, {
    key: "_sanitizeMessage",
    value: function _sanitizeMessage(message) {
      // Create a deep copy to avoid modifying the original
      var sanitized = JSON.parse(JSON.stringify(message));

      // Sanitize string values to prevent XSS
      var sanitizeValue = function sanitizeValue(value) {
        if (typeof value === 'string') {
          // Basic XSS protection
          return value.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#x27;').replace(/\//g, '&#x2F;');
        }
        return value;
      };

      // Recursively sanitize all string values
      var _sanitizeObject = function sanitizeObject(obj) {
        if (!obj || (0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(obj) !== 'object') return obj;
        if (Array.isArray(obj)) {
          return obj.map(function (item) {
            return _sanitizeObject(item);
          });
        }
        var result = {};
        for (var key in obj) {
          if (Object.prototype.hasOwnProperty.call(obj, key)) {
            var value = obj[key];
            if ((0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(value) === 'object' && value !== null) {
              result[key] = _sanitizeObject(value);
            } else {
              result[key] = sanitizeValue(value);
            }
          }
        }
        return result;
      };
      return _sanitizeObject(sanitized);
    }

    // Compress a message
  }, {
    key: "_compressMessage",
    value: function _compressMessage(message) {
      // In a real implementation, this would use a compression algorithm
      // For this example, we'll use a simple Base64 encoding as a placeholder
      try {
        return btoa(message);
      } catch (error) {
        console.error('Error compressing message:', error);
        return message;
      }
    }

    // Decompress a message
  }, {
    key: "_decompressMessage",
    value: function _decompressMessage(message) {
      // In a real implementation, this would use a decompression algorithm
      // For this example, we'll use a simple Base64 decoding as a placeholder
      try {
        return atob(message);
      } catch (error) {
        console.error('Error decompressing message:', error);
        return message;
      }
    }

    // Add a message to the batch
  }, {
    key: "_addToBatch",
    value: function _addToBatch(message, options) {
      var _this4 = this;
      // Initialize batch if not exists
      if (!this._batch) {
        this._batch = {
          messages: [],
          timer: null
        };
      }

      // Add message to batch
      this._batch.messages.push({
        message: message,
        options: options
      });

      // Start batch timer if not already running
      if (!this._batch.timer) {
        this._batch.timer = setTimeout(function () {
          _this4._sendBatch();
        }, this.performanceOptions.batchInterval);
      }

      // Send batch immediately if it reaches the max size
      if (this._batch.messages.length >= this.performanceOptions.maxBatchSize) {
        clearTimeout(this._batch.timer);
        this._batch.timer = null;
        this._sendBatch();
      }
    }

    // Send a batch of messages
  }, {
    key: "_sendBatch",
    value: function _sendBatch() {
      var _this5 = this;
      if (!this._batch || this._batch.messages.length === 0) {
        return;
      }

      // Create batch message
      var batchMessage = {
        type: 'batch',
        messages: this._batch.messages.map(function (item) {
          return item.message;
        }),
        timestamp: Date.now(),
        count: this._batch.messages.length
      };

      // Clear batch
      var messages = this._batch.messages;
      this._batch.messages = [];
      this._batch.timer = null;

      // Send batch
      this.send(batchMessage, {
        immediate: true
      })["catch"](function (error) {
        console.error('Error sending batch:', error);

        // Re-queue messages on error
        messages.forEach(function (item) {
          _this5.queueMessage(item.message, item.options);
        });
      });
    }

    // Process rate limit queue
  }, {
    key: "_processRateLimitQueue",
    value: function _processRateLimitQueue() {
      var _this6 = this;
      if (!this._rateLimitState || this._rateLimitState.queue.length === 0) {
        if (this._rateLimitState) {
          this._rateLimitState.processingQueue = false;
        }
        return;
      }
      var now = Date.now();
      var state = this._rateLimitState;
      var maxMessagesPerSecond = this.securityOptions.rateLimiting.maxMessagesPerSecond;

      // Reset window if needed
      if (now - state.windowStart > 1000) {
        state.messageCount = 0;
        state.windowStart = now;
      }

      // Check if we can send more messages
      if (state.messageCount < maxMessagesPerSecond) {
        // Get the next message
        var item = state.queue.shift();

        // Send the message
        this.send(item.message, _objectSpread(_objectSpread({}, item.options), {}, {
          immediate: true
        }))["catch"](function (error) {
          console.error('Error sending queued message:', error);
        });

        // Update counter
        state.messageCount++;

        // Schedule next processing
        if (state.queue.length > 0) {
          setTimeout(function () {
            return _this6._processRateLimitQueue();
          }, 50);
        } else {
          state.processingQueue = false;
        }
      } else {
        // Wait until we can send more messages
        setTimeout(function () {
          return _this6._processRateLimitQueue();
        }, 100);
      }
    }

    // Alias for send
  }, {
    key: "sendMessage",
    value: function sendMessage(message) {
      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      return this.send(message, options);
    }

    // Queue a message to be sent when connection is established
  }, {
    key: "queueMessage",
    value: function queueMessage(message) {
      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      if (this.debug) {
        console.log('Queuing message for later delivery:', message);
      }
      this.offlineQueue.push({
        message: message,
        options: options
      });

      // Trigger event
      this.triggerEvent('message_queued_offline', {
        message: message,
        queueLength: this.offlineQueue.length
      });
      return this.offlineQueue.length;
    }

    // Process queued messages
  }, {
    key: "processOfflineQueue",
    value: function processOfflineQueue() {
      var _this7 = this;
      if (this.offlineQueue.length === 0) {
        return;
      }
      if (this.debug) {
        console.log("Processing offline queue (".concat(this.offlineQueue.length, " messages)"));
      }

      // Create a copy of the queue and clear it
      var queue = (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(this.offlineQueue);
      this.offlineQueue = [];

      // Process each message
      queue.forEach(function (_ref) {
        var message = _ref.message,
          options = _ref.options;
        _this7.send(message, options)["catch"](function (error) {
          console.error('Error sending queued message:', error);
        });
      });

      // Trigger event
      this.triggerEvent('offline_queue_processed', {
        processedCount: queue.length
      });
    }

    // Start heartbeat to keep connection alive
  }, {
    key: "startHeartbeat",
    value: function startHeartbeat() {
      var _this8 = this;
      this.stopHeartbeat();
      this.missedHeartbeats = 0;
      this.heartbeatTimeoutId = setInterval(function () {
        if (!_this8.isConnected()) {
          _this8.stopHeartbeat();
          return;
        }
        _this8.missedHeartbeats++;
        if (_this8.missedHeartbeats >= _this8.maxMissedHeartbeats) {
          if (_this8.debug) {
            console.warn("Missed ".concat(_this8.missedHeartbeats, " heartbeats, reconnecting..."));
          }
          _this8.triggerEvent('heartbeat_timeout', {
            missed: _this8.missedHeartbeats,
            max: _this8.maxMissedHeartbeats
          });
          _this8.reconnect();
          return;
        }

        // Send heartbeat
        _this8.send({
          type: 'ping',
          timestamp: Date.now()
        })["catch"](function (error) {
          console.error('Error sending heartbeat:', error);
        });
      }, this.heartbeatInterval);
    }

    // Stop heartbeat
  }, {
    key: "stopHeartbeat",
    value: function stopHeartbeat() {
      if (this.heartbeatTimeoutId) {
        clearInterval(this.heartbeatTimeoutId);
        this.heartbeatTimeoutId = null;
      }
    }

    // Add event listener
  }, {
    key: "addEventListener",
    value: function addEventListener(event, callback) {
      if (!this.eventListeners[event]) {
        this.eventListeners[event] = [];
      }
      this.eventListeners[event].push(callback);
      return this;
    }

    // Remove event listener
  }, {
    key: "removeEventListener",
    value: function removeEventListener(event, callback) {
      if (!this.eventListeners[event]) {
        return this;
      }
      this.eventListeners[event] = this.eventListeners[event].filter(function (cb) {
        return cb !== callback;
      });
      return this;
    }

    // Alias for addEventListener (for compatibility)
  }, {
    key: "on",
    value: function on(event, callback) {
      return this.addEventListener(event, callback);
    }

    // Alias for removeEventListener (for compatibility)
  }, {
    key: "off",
    value: function off(event, callback) {
      return this.removeEventListener(event, callback);
    }

    // Trigger event
  }, {
    key: "triggerEvent",
    value: function triggerEvent(event, data) {
      if (!this.eventListeners[event]) {
        return;
      }
      this.eventListeners[event].forEach(function (callback) {
        try {
          callback(data);
        } catch (error) {
          console.error("Error in ".concat(event, " event listener:"), error);
        }
      });
    }

    // Get connection state
  }, {
    key: "getConnectionState",
    value: function getConnectionState() {
      var readyStateText = 'CLOSED';
      if (this.socket) {
        switch (this.socket.readyState) {
          case WebSocket.CONNECTING:
            readyStateText = 'CONNECTING';
            break;
          case WebSocket.OPEN:
            readyStateText = 'OPEN';
            break;
          case WebSocket.CLOSING:
            readyStateText = 'CLOSING';
            break;
          case WebSocket.CLOSED:
            readyStateText = 'CLOSED';
            break;
        }
      }
      return {
        connected: this.isConnected(),
        connecting: this.connecting,
        readyState: this.socket ? this.socket.readyState : WebSocket.CLOSED,
        readyStateText: readyStateText,
        reconnectAttempts: this.reconnectAttempts,
        maxReconnectAttempts: this.maxReconnectAttempts,
        url: this.url,
        lastError: this.lastError
      };
    }

    // Get offline queue status
  }, {
    key: "getOfflineQueueStatus",
    value: function getOfflineQueueStatus() {
      return {
        count: this.offlineQueue.length,
        enabled: this.options.queueOfflineMessages
      };
    }

    // Clear offline queue
  }, {
    key: "clearOfflineQueue",
    value: function clearOfflineQueue() {
      var count = this.offlineQueue.length;
      this.offlineQueue = [];
      this.triggerEvent('offline_queue_cleared', {
        count: count
      });
      return count;
    }

    // Get singleton instance
  }, {
    key: "_getDefaultWebSocketUrl",
    value:
    // Get default WebSocket URL
    function _getDefaultWebSocketUrl() {
      var protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      var host = window.location.host;
      return "".concat(protocol, "//").concat(host, "/ws");
    }

    // Create a specific error type
  }, {
    key: "_createError",
    value: function _createError(type, message) {
      var details = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      var error = new Error(message);
      error.type = type;
      error.details = details;
      error.timestamp = Date.now();

      // Log error if debug is enabled
      if (this.debug) {
        console.error("WebSocketError [".concat(type, "]: ").concat(message), details);
      }

      // Trigger error event
      this.triggerEvent('error', error);
      return error;
    }

    // Handle connection errors with recovery strategies
  }, {
    key: "_handleConnectionError",
    value: function _handleConnectionError(error) {
      var _this9 = this;
      var attempt = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;
      // Store the error
      this.lastError = error;

      // Determine error type
      var errorType = 'CONNECTION_ERROR';
      var errorMessage = 'Failed to connect to WebSocket server';
      if (error.code) {
        switch (error.code) {
          case 1000:
            errorType = 'NORMAL_CLOSURE';
            errorMessage = 'Connection closed normally';
            break;
          case 1001:
            errorType = 'GOING_AWAY';
            errorMessage = 'Server is going away';
            break;
          case 1002:
            errorType = 'PROTOCOL_ERROR';
            errorMessage = 'Protocol error';
            break;
          case 1003:
            errorType = 'UNSUPPORTED_DATA';
            errorMessage = 'Unsupported data';
            break;
          case 1005:
            errorType = 'NO_STATUS';
            errorMessage = 'No status code was provided';
            break;
          case 1006:
            errorType = 'ABNORMAL_CLOSURE';
            errorMessage = 'Connection closed abnormally';
            break;
          case 1007:
            errorType = 'INVALID_FRAME_PAYLOAD';
            errorMessage = 'Invalid frame payload data';
            break;
          case 1008:
            errorType = 'POLICY_VIOLATION';
            errorMessage = 'Policy violation';
            break;
          case 1009:
            errorType = 'MESSAGE_TOO_BIG';
            errorMessage = 'Message too big';
            break;
          case 1010:
            errorType = 'MISSING_EXTENSION';
            errorMessage = 'Required extension is missing';
            break;
          case 1011:
            errorType = 'INTERNAL_ERROR';
            errorMessage = 'Internal server error';
            break;
          case 1012:
            errorType = 'SERVICE_RESTART';
            errorMessage = 'Service is restarting';
            break;
          case 1013:
            errorType = 'TRY_AGAIN_LATER';
            errorMessage = 'Try again later';
            break;
          case 1014:
            errorType = 'BAD_GATEWAY';
            errorMessage = 'Bad gateway';
            break;
          case 1015:
            errorType = 'TLS_HANDSHAKE_FAILURE';
            errorMessage = 'TLS handshake failure';
            break;
          default:
            errorType = "CODE_".concat(error.code);
            errorMessage = "WebSocket error code ".concat(error.code);
        }
      } else if (error.message) {
        if (error.message.includes('timeout')) {
          errorType = 'CONNECTION_TIMEOUT';
          errorMessage = 'Connection timed out';
        } else if (error.message.includes('refused')) {
          errorType = 'CONNECTION_REFUSED';
          errorMessage = 'Connection refused';
        } else if (error.message.includes('ENOTFOUND')) {
          errorType = 'HOST_NOT_FOUND';
          errorMessage = 'Host not found';
        }
      }

      // Create error object
      var wsError = this._createError(errorType, errorMessage, {
        originalError: error,
        attempt: attempt,
        url: this.url
      });

      // Apply recovery strategy based on error type
      switch (errorType) {
        case 'NORMAL_CLOSURE':
          // No recovery needed for normal closure
          break;
        case 'GOING_AWAY':
        case 'SERVICE_RESTART':
          // Server is restarting, wait a bit longer before reconnecting
          if (this.options.autoReconnect) {
            setTimeout(function () {
              return _this9.reconnect();
            }, 5000);
          }
          break;
        case 'TRY_AGAIN_LATER':
          // Server is busy, wait a bit longer before reconnecting
          if (this.options.autoReconnect) {
            setTimeout(function () {
              return _this9.reconnect();
            }, 10000);
          }
          break;
        case 'CONNECTION_REFUSED':
        case 'HOST_NOT_FOUND':
          // Server might be down, try alternative URL if available
          if (this.options.fallbackUrls && this.options.fallbackUrls.length > 0) {
            var fallbackUrl = this.options.fallbackUrls[attempt % this.options.fallbackUrls.length];
            if (this.debug) {
              console.log("Trying fallback URL: ".concat(fallbackUrl));
            }
            this.url = fallbackUrl;
            if (this.options.autoReconnect) {
              setTimeout(function () {
                return _this9.reconnect();
              }, 1000);
            }
          } else if (this.options.autoReconnect) {
            // No fallback URLs, just reconnect to the same URL
            this.reconnect();
          }
          break;
        case 'ABNORMAL_CLOSURE':
        case 'INTERNAL_ERROR':
        case 'BAD_GATEWAY':
          // Server error, reconnect with exponential backoff
          if (this.options.autoReconnect) {
            var delay = Math.min(1000 * Math.pow(2, attempt), 30000);
            setTimeout(function () {
              return _this9.reconnect();
            }, delay);
          }
          break;
        default:
          // For other errors, use the default reconnect strategy
          if (this.options.autoReconnect) {
            this.reconnect();
          }
      }
      return wsError;
    }

    // Handle message errors
  }, {
    key: "_handleMessageError",
    value: function _handleMessageError(error, message) {
      // Determine error type
      var errorType = 'MESSAGE_ERROR';
      var errorMessage = 'Failed to process WebSocket message';
      if (error.message) {
        if (error.message.includes('JSON')) {
          errorType = 'INVALID_JSON';
          errorMessage = 'Invalid JSON in message';
        } else if (error.message.includes('timeout')) {
          errorType = 'MESSAGE_TIMEOUT';
          errorMessage = 'Message processing timed out';
        } else if (error.message.includes('rate limit')) {
          errorType = 'RATE_LIMITED';
          errorMessage = 'Rate limit exceeded';
        }
      }

      // Create error object
      return this._createError(errorType, errorMessage, {
        originalError: error,
        message: message
      });
    }
  }], [{
    key: "getInstance",
    value: function getInstance(options) {
      if (!WebSocketService.instance) {
        WebSocketService.instance = new WebSocketService();
        if (options) {
          WebSocketService.instance.init(options);
        }
      }
      return WebSocketService.instance;
    }
  }]);
}(); // Create and export a singleton instance that's always defined
var webSocketServiceInstance = new WebSocketService();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (webSocketServiceInstance);

/***/ }),

/***/ 17648:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony export initErrorTracking */
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

/**
 * Error Tracking System
 * 
 * This utility provides comprehensive error tracking and reporting functionality.
 * It captures unhandled errors, console errors, network errors, and more.
 */

// Configuration
var config = {
  // Whether error tracking is enabled
  enabled: true,
  // Sampling rate (0.0 to 1.0) - what percentage of errors to track
  samplingRate: 1.0,
  // Maximum number of errors to store
  errorLimit: 100,
  // Maximum number of breadcrumbs to store
  breadcrumbLimit: 50,
  // Errors to ignore (regexes)
  ignoredErrors: [/ResizeObserver loop limit exceeded/, /Loading chunk \d+ failed/, /Network request failed/, /Script error/, /Extension context invalidated/, /Failed to report error/, /Error reporting failed/, /TypeError: Failed to fetch/, /TypeError: NetworkError when attempting to fetch resource/, /AbortError/, /Request aborted/, /Request timed out/, /Load failed/],
  // Endpoint to report errors to
  reportingEndpoint: '/api/errors',
  // Whether to log errors to console
  logToConsole: true,
  // Whether to capture console errors
  captureConsoleErrors: true,
  // Whether to capture network errors
  captureNetworkErrors: true,
  // Whether to capture unhandled rejections
  captureUnhandledRejections: true,
  // Whether to capture breadcrumbs
  captureBreadcrumbs: true
};

// Circuit breaker for error reporting
var circuitBreaker = {
  state: 'CLOSED',
  // CLOSED, OPEN, HALF_OPEN
  failureCount: 0,
  lastFailureTime: null,
  failureThreshold: 5,
  // Open circuit after 5 consecutive failures
  timeout: 60000,
  // 1 minute timeout before trying again
  successThreshold: 2 // Number of successes needed to close circuit
};

// Error storage
var errorStore = {
  errors: [],
  breadcrumbs: [],
  sessionId: generateSessionId(),
  startTime: new Date().toISOString(),
  reportingQueue: [],
  // Queue for failed reports
  lastReportAttempt: null,
  reportingInProgress: false
};

/**
 * Generate a unique session ID
 * 
 * @returns {string} - A unique session ID
 */
function generateSessionId() {
  return "".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9));
}

/**
 * Initialize the error tracking system
 * 
 * @param {Object} options - Configuration options
 * @returns {Object} - The error tracking API
 */
function initErrorTracking() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  // Merge options with default config
  Object.assign(config, options);
  if (!config.enabled) {
    console.log('Error tracking is disabled');
    return createPublicApi();
  }

  // Set up global error handlers
  setupErrorHandlers();

  // Log initialization
  console.log('Error tracking initialized');

  // Return the public API
  return createPublicApi();
}

/**
 * Set up global error handlers
 */
function setupErrorHandlers() {
  // Handle unhandled errors
  window.addEventListener('error', handleGlobalError);

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', handleUnhandledRejection);

  // Capture console errors
  if (config.captureConsoleErrors) {
    setupConsoleCapture();
  }

  // Capture network errors
  if (config.captureNetworkErrors) {
    setupNetworkCapture();
  }

  // Capture breadcrumbs
  if (config.captureBreadcrumbs) {
    setupBreadcrumbCapture();
  }
}

/**
 * Handle global errors
 * 
 * @param {ErrorEvent} event - The error event
 */
function handleGlobalError(event) {
  // Prevent default browser error handling
  event.preventDefault();

  // Track the error
  _trackError({
    type: 'uncaught_error',
    message: event.message || 'Unknown error',
    stack: event.error ? event.error.stack : null,
    source: event.filename,
    line: event.lineno,
    column: event.colno,
    timestamp: new Date().toISOString()
  });

  // Log to console if enabled
  if (config.logToConsole) {
    console.error('Uncaught error:', event.message);
  }
}

/**
 * Handle unhandled promise rejections
 * 
 * @param {PromiseRejectionEvent} event - The rejection event
 */
function handleUnhandledRejection(event) {
  // Prevent default browser error handling
  event.preventDefault();

  // Get error details
  var error = event.reason;
  var message = error instanceof Error ? error.message : String(error);
  var stack = error instanceof Error ? error.stack : null;

  // Track the error
  _trackError({
    type: 'unhandled_rejection',
    message: message || 'Unhandled promise rejection',
    stack: stack,
    timestamp: new Date().toISOString()
  });

  // Log to console if enabled
  if (config.logToConsole) {
    console.error('Unhandled rejection:', message);
  }
}

/**
 * Set up console error capture
 */
function setupConsoleCapture() {
  // Save original console methods
  var originalConsoleError = console.error;
  var originalConsoleWarn = console.warn;

  // Override console.error
  console.error = function () {
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    // Call original method
    originalConsoleError.apply(console, args);

    // Track the error
    var message = args.map(function (arg) {
      return typeof arg === 'string' ? arg : JSON.stringify(arg);
    }).join(' ');
    _trackError({
      type: 'console_error',
      message: message,
      timestamp: new Date().toISOString()
    });
  };

  // Override console.warn
  console.warn = function () {
    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
      args[_key2] = arguments[_key2];
    }
    // Call original method
    originalConsoleWarn.apply(console, args);

    // Add breadcrumb
    if (config.captureBreadcrumbs) {
      _addBreadcrumb({
        type: 'console_warn',
        message: args.map(function (arg) {
          return typeof arg === 'string' ? arg : JSON.stringify(arg);
        }).join(' '),
        timestamp: new Date().toISOString()
      });
    }
  };
}

/**
 * Set up network error capture
 */
function setupNetworkCapture() {
  // Store original fetch for error reporting
  if (!window._originalFetch) {
    window._originalFetch = window.fetch;
  }
  var originalFetch = window._originalFetch;

  // Override fetch
  window.fetch = /*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee() {
    var _len3,
      args,
      _key3,
      response,
      _args$,
      _args$2,
      _args$3,
      _args = arguments,
      _t;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          for (_len3 = _args.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
            args[_key3] = _args[_key3];
          }
          _context.prev = 1;
          _context.next = 2;
          return originalFetch.apply(window, args);
        case 2:
          response = _context.sent;
          // Add breadcrumb for successful requests
          if (config.captureBreadcrumbs) {
            _addBreadcrumb({
              type: 'network',
              category: 'fetch',
              data: {
                url: typeof args[0] === 'string' ? args[0] : args[0].url,
                method: ((_args$ = args[1]) === null || _args$ === void 0 ? void 0 : _args$.method) || 'GET',
                status: response.status
              },
              timestamp: new Date().toISOString()
            });
          }

          // Track error responses
          if (!response.ok) {
            _trackError({
              type: 'network_error',
              message: "Fetch error: ".concat(response.status, " ").concat(response.statusText),
              data: {
                url: typeof args[0] === 'string' ? args[0] : args[0].url,
                method: ((_args$2 = args[1]) === null || _args$2 === void 0 ? void 0 : _args$2.method) || 'GET',
                status: response.status,
                statusText: response.statusText
              },
              timestamp: new Date().toISOString()
            });
          }
          return _context.abrupt("return", response);
        case 3:
          _context.prev = 3;
          _t = _context["catch"](1);
          // Track network errors
          _trackError({
            type: 'network_error',
            message: "Fetch failed: ".concat(_t.message),
            stack: _t.stack,
            data: {
              url: typeof args[0] === 'string' ? args[0] : (_args$3 = args[0]) === null || _args$3 === void 0 ? void 0 : _args$3.url
            },
            timestamp: new Date().toISOString()
          });
          throw _t;
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[1, 3]]);
  }));

  // Override XMLHttpRequest
  var originalXhrOpen = XMLHttpRequest.prototype.open;
  var originalXhrSend = XMLHttpRequest.prototype.send;
  XMLHttpRequest.prototype.open = function (method, url) {
    this._errorTracking = {
      method: method,
      url: url
    };
    return originalXhrOpen.apply(this, arguments);
  };
  XMLHttpRequest.prototype.send = function () {
    // Add event listeners
    this.addEventListener('load', function () {
      // Add breadcrumb for successful requests
      if (config.captureBreadcrumbs) {
        var _this$_errorTracking, _this$_errorTracking2;
        _addBreadcrumb({
          type: 'network',
          category: 'xhr',
          data: {
            url: (_this$_errorTracking = this._errorTracking) === null || _this$_errorTracking === void 0 ? void 0 : _this$_errorTracking.url,
            method: (_this$_errorTracking2 = this._errorTracking) === null || _this$_errorTracking2 === void 0 ? void 0 : _this$_errorTracking2.method,
            status: this.status
          },
          timestamp: new Date().toISOString()
        });
      }

      // Track error responses
      if (this.status >= 400) {
        var _this$_errorTracking3, _this$_errorTracking4;
        _trackError({
          type: 'network_error',
          message: "XHR error: ".concat(this.status, " ").concat(this.statusText),
          data: {
            url: (_this$_errorTracking3 = this._errorTracking) === null || _this$_errorTracking3 === void 0 ? void 0 : _this$_errorTracking3.url,
            method: (_this$_errorTracking4 = this._errorTracking) === null || _this$_errorTracking4 === void 0 ? void 0 : _this$_errorTracking4.method,
            status: this.status,
            statusText: this.statusText
          },
          timestamp: new Date().toISOString()
        });
      }
    });
    this.addEventListener('error', function () {
      var _this$_errorTracking5, _this$_errorTracking6;
      // Track network errors
      _trackError({
        type: 'network_error',
        message: 'XHR failed',
        data: {
          url: (_this$_errorTracking5 = this._errorTracking) === null || _this$_errorTracking5 === void 0 ? void 0 : _this$_errorTracking5.url,
          method: (_this$_errorTracking6 = this._errorTracking) === null || _this$_errorTracking6 === void 0 ? void 0 : _this$_errorTracking6.method
        },
        timestamp: new Date().toISOString()
      });
    });
    return originalXhrSend.apply(this, arguments);
  };
}

/**
 * Set up breadcrumb capture
 */
function setupBreadcrumbCapture() {
  // Capture clicks
  document.addEventListener('click', function (event) {
    var _element$innerText;
    // Get the clicked element
    var element = event.target;

    // Get element details
    var tagName = element.tagName.toLowerCase();
    var id = element.id ? "#".concat(element.id) : '';
    var classes = Array.from(element.classList).map(function (c) {
      return ".".concat(c);
    }).join('');
    var text = (_element$innerText = element.innerText) === null || _element$innerText === void 0 ? void 0 : _element$innerText.substring(0, 50);

    // Add breadcrumb
    _addBreadcrumb({
      type: 'user',
      category: 'click',
      data: {
        element: "".concat(tagName).concat(id).concat(classes),
        text: text
      },
      timestamp: new Date().toISOString()
    });
  });

  // Capture navigation
  window.addEventListener('popstate', function () {
    _addBreadcrumb({
      type: 'navigation',
      data: {
        from: document.referrer,
        to: window.location.href
      },
      timestamp: new Date().toISOString()
    });
  });
}

/**
 * Track an error
 * 
 * @param {Object} error - The error to track
 */
function _trackError(error) {
  // Check if error tracking is enabled
  if (!config.enabled) {
    return;
  }

  // Apply sampling rate
  if (Math.random() > config.samplingRate) {
    return;
  }

  // Check if error should be ignored
  if (shouldIgnoreError(error)) {
    return;
  }

  // Add session information
  error.sessionId = errorStore.sessionId;

  // Add user agent
  error.userAgent = navigator.userAgent;

  // Add URL
  error.url = window.location.href;

  // Add breadcrumbs
  error.breadcrumbs = (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(errorStore.breadcrumbs);

  // Add to error store
  errorStore.errors.push(error);

  // Limit the number of stored errors
  if (errorStore.errors.length > config.errorLimit) {
    errorStore.errors.shift();
  }

  // Report the error
  reportError(error);
}

/**
 * Check if an error should be ignored
 * 
 * @param {Object} error - The error to check
 * @returns {boolean} - Whether the error should be ignored
 */
function shouldIgnoreError(error) {
  // Check against ignored errors
  return config.ignoredErrors.some(function (pattern) {
    if (pattern instanceof RegExp) {
      return pattern.test(error.message);
    }
    return error.message.includes(pattern);
  });
}

/**
 * Add a breadcrumb
 * 
 * @param {Object} breadcrumb - The breadcrumb to add
 */
function _addBreadcrumb(breadcrumb) {
  // Check if breadcrumb tracking is enabled
  if (!config.captureBreadcrumbs) {
    return;
  }

  // Add to breadcrumb store
  errorStore.breadcrumbs.push(breadcrumb);

  // Limit the number of stored breadcrumbs
  if (errorStore.breadcrumbs.length > config.breadcrumbLimit) {
    errorStore.breadcrumbs.shift();
  }
}

/**
 * Check if circuit breaker allows error reporting
 * @returns {boolean} - Whether reporting is allowed
 */
function canReportError() {
  var now = Date.now();
  switch (circuitBreaker.state) {
    case 'OPEN':
      // Check if timeout has passed
      if (now - circuitBreaker.lastFailureTime >= circuitBreaker.timeout) {
        circuitBreaker.state = 'HALF_OPEN';
        return true;
      }
      return false;
    case 'HALF_OPEN':
    case 'CLOSED':
      return true;
    default:
      return true;
  }
}

/**
 * Handle successful error reporting
 */
function onReportSuccess() {
  if (circuitBreaker.state === 'HALF_OPEN') {
    circuitBreaker.successThreshold--;
    if (circuitBreaker.successThreshold <= 0) {
      circuitBreaker.state = 'CLOSED';
      circuitBreaker.failureCount = 0;
      circuitBreaker.successThreshold = 2; // Reset
    }
  } else if (circuitBreaker.state === 'CLOSED') {
    circuitBreaker.failureCount = 0;
  }
}

/**
 * Handle failed error reporting
 */
function onReportFailure() {
  circuitBreaker.failureCount++;
  circuitBreaker.lastFailureTime = Date.now();
  if (circuitBreaker.failureCount >= circuitBreaker.failureThreshold) {
    circuitBreaker.state = 'OPEN';
  }
}

/**
 * Store error locally as fallback when reporting fails
 * @param {Object} error - The error to store
 */
function storeErrorLocally(error) {
  try {
    var key = 'errorTracker_failedReports';
    var stored = localStorage.getItem(key);
    var failedReports = stored ? JSON.parse(stored) : [];

    // Add timestamp and limit storage
    failedReports.push(_objectSpread(_objectSpread({}, error), {}, {
      storedAt: new Date().toISOString()
    }));

    // Keep only last 50 failed reports
    if (failedReports.length > 50) {
      failedReports.splice(0, failedReports.length - 50);
    }
    localStorage.setItem(key, JSON.stringify(failedReports));
  } catch (storageError) {
    // Ignore storage errors
    if (config.logToConsole && "production" === 'development') {}
  }
}

/**
 * Report an error to the server with circuit breaker and retry logic
 *
 * @param {Object} error - The error to report
 * @param {number} retryCount - Current retry attempt (default: 0)
 * @param {number} maxRetries - Maximum number of retries (default: 3)
 */
function reportError(error) {
  var _error$message, _error$url;
  var retryCount = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
  var maxRetries = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 3;
  // Check if reporting is enabled
  if (!config.reportingEndpoint) {
    return;
  }

  // Check circuit breaker
  if (!canReportError()) {
    // Add to queue for later retry
    errorStore.reportingQueue.push(error);
    return;
  }

  // Prevent reporting errors about error reporting to avoid infinite loops
  if (error.type === 'error_reporting_failure' || (_error$message = error.message) !== null && _error$message !== void 0 && _error$message.includes('Failed to report error') || (_error$url = error.url) !== null && _error$url !== void 0 && _error$url.includes(config.reportingEndpoint)) {
    return;
  }

  // Prevent concurrent reporting
  if (errorStore.reportingInProgress) {
    errorStore.reportingQueue.push(error);
    return;
  }
  errorStore.reportingInProgress = true;
  errorStore.lastReportAttempt = Date.now();

  // Calculate delay for exponential backoff
  var delay = Math.min(1000 * Math.pow(2, retryCount), 30000); // Max 30 seconds

  var attemptReport = function attemptReport() {
    // Use original fetch to avoid infinite loops with network error tracking
    var originalFetch = window._originalFetch || window.fetch;
    originalFetch(config.reportingEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(error),
      keepalive: true
    }).then(function (response) {
      if (response.ok) {
        onReportSuccess();

        // Process queued errors if circuit is now closed
        if (circuitBreaker.state === 'CLOSED' && errorStore.reportingQueue.length > 0) {
          var queuedError = errorStore.reportingQueue.shift();
          setTimeout(function () {
            return reportError(queuedError);
          }, 100);
        }
      } else {
        throw new Error("HTTP ".concat(response.status, ": ").concat(response.statusText));
      }
    })["catch"](function (err) {
      onReportFailure();

      // Retry with exponential backoff if we haven't exceeded max retries
      if (retryCount < maxRetries) {
        setTimeout(function () {
          reportError(error, retryCount + 1, maxRetries);
        }, delay);
      } else {
        // Store in local storage as fallback
        storeErrorLocally(error);

        // Log final failure only in development
        if (config.logToConsole && "production" === 'development') {}
      }
    })["finally"](function () {
      errorStore.reportingInProgress = false;
    });
  };

  // Apply initial delay for retries
  if (retryCount > 0) {
    setTimeout(attemptReport, delay);
  } else {
    attemptReport();
  }
}

/**
 * Create the public API
 * 
 * @returns {Object} - The public API
 */
function createPublicApi() {
  return {
    /**
     * Track an error manually
     * 
     * @param {Error|string} error - The error to track
     * @param {Object} additionalData - Additional data to include
     */
    trackError: function trackError(error) {
      var additionalData = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      // Convert error to the right format
      var errorObj = error instanceof Error ? _objectSpread(_objectSpread({
        type: 'manual',
        message: error.message,
        stack: error.stack
      }, additionalData), {}, {
        timestamp: new Date().toISOString()
      }) : _objectSpread(_objectSpread({
        type: 'manual',
        message: String(error)
      }, additionalData), {}, {
        timestamp: new Date().toISOString()
      });

      // Track the error
      _trackError(errorObj);
    },
    /**
     * Add a breadcrumb manually
     * 
     * @param {string} message - The breadcrumb message
     * @param {string} category - The breadcrumb category
     * @param {Object} data - Additional data to include
     */
    addBreadcrumb: function addBreadcrumb(message) {
      var category = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'manual';
      var data = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      _addBreadcrumb({
        type: 'manual',
        category: category,
        message: message,
        data: data,
        timestamp: new Date().toISOString()
      });
    },
    /**
     * Get all tracked errors
     * 
     * @returns {Array} - The tracked errors
     */
    getErrors: function getErrors() {
      return (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(errorStore.errors);
    },
    /**
     * Get all breadcrumbs
     * 
     * @returns {Array} - The breadcrumbs
     */
    getBreadcrumbs: function getBreadcrumbs() {
      return (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(errorStore.breadcrumbs);
    },
    /**
     * Clear all tracked errors
     */
    clearErrors: function clearErrors() {
      errorStore.errors = [];
    },
    /**
     * Clear all breadcrumbs
     */
    clearBreadcrumbs: function clearBreadcrumbs() {
      errorStore.breadcrumbs = [];
    },
    /**
     * Get the current configuration
     * 
     * @returns {Object} - The current configuration
     */
    getConfig: function getConfig() {
      return _objectSpread({}, config);
    },
    /**
     * Update the configuration
     *
     * @param {Object} options - The new configuration options
     */
    updateConfig: function updateConfig(options) {
      Object.assign(config, options);
    },
    /**
     * Get circuit breaker status
     *
     * @returns {Object} - Circuit breaker status
     */
    getCircuitBreakerStatus: function getCircuitBreakerStatus() {
      return {
        state: circuitBreaker.state,
        failureCount: circuitBreaker.failureCount,
        lastFailureTime: circuitBreaker.lastFailureTime,
        queueLength: errorStore.reportingQueue.length
      };
    },
    /**
     * Manually retry queued errors
     *
     * @returns {Promise<void>} - Promise that resolves when retry is complete
     */
    retryQueuedErrors: function retryQueuedErrors() {
      return new Promise(function (resolve) {
        if (errorStore.reportingQueue.length === 0) {
          resolve();
          return;
        }

        // Reset circuit breaker to allow retries
        if (circuitBreaker.state === 'OPEN') {
          circuitBreaker.state = 'HALF_OPEN';
        }

        // Process one queued error
        var queuedError = errorStore.reportingQueue.shift();
        reportError(queuedError);

        // Wait a bit and resolve
        setTimeout(resolve, 1000);
      });
    },
    /**
     * Get locally stored failed reports
     *
     * @returns {Array} - Array of failed reports from localStorage
     */
    getLocallyStoredErrors: function getLocallyStoredErrors() {
      try {
        var key = 'errorTracker_failedReports';
        var stored = localStorage.getItem(key);
        return stored ? JSON.parse(stored) : [];
      } catch (error) {
        return [];
      }
    },
    /**
     * Clear locally stored failed reports
     */
    clearLocallyStoredErrors: function clearLocallyStoredErrors() {
      try {
        localStorage.removeItem('errorTracker_failedReports');
      } catch (error) {
        // Ignore storage errors
      }
    }
  };
}

// Create and export the default instance
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (initErrorTracking());

/***/ }),

/***/ 48035:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  A: () => (/* binding */ redux_store)
});

// EXTERNAL MODULE: ./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs
var redux_toolkit_modern = __webpack_require__(20038);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./src/redux/actions.js
var actions = __webpack_require__(81616);
;// ./src/redux/reducers.js


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Redux Reducers for the App Builder
 */


// Initial state
var initialState = {
  // App data structure
  app: {
    // App data
    components: [],
    layouts: [],
    styles: {},
    data: {}
  },
  // WebSocket state
  websocket: {
    connected: false,
    connecting: false,
    url: null,
    error: null,
    lastMessage: null,
    reconnectAttempts: 0
  },
  // UI state
  loading: false,
  error: null,
  // Theme state
  themes: [],
  activeTheme: 'default'
};

/**
 * Root reducer
 * @param {Object} state - Current state
 * @param {Object} action - Action object
 * @returns {Object} New state
 */
var rootReducer = function rootReducer() {
  var _action$payload, _action$payload2, _state$app, _state$app2, _state$app3, _state$app4, _state$app5, _state$app6, _state$app7, _state$app8, _state$app9, _state$app0;
  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;
  var action = arguments.length > 1 ? arguments[1] : undefined;
  switch (action.type) {
    // WebSocket actions
    case actions/* ActionTypes */.Q3.WS_CONNECT:
      return _objectSpread(_objectSpread({}, state), {}, {
        websocket: _objectSpread(_objectSpread({}, state.websocket), {}, {
          connecting: true,
          url: ((_action$payload = action.payload) === null || _action$payload === void 0 ? void 0 : _action$payload.url) || state.websocket.url,
          error: null
        })
      });
    case actions/* ActionTypes */.Q3.WS_CONNECTED:
      return _objectSpread(_objectSpread({}, state), {}, {
        websocket: _objectSpread(_objectSpread({}, state.websocket), {}, {
          connected: true,
          connecting: false,
          error: null,
          reconnectAttempts: 0
        })
      });
    case actions/* ActionTypes */.Q3.WS_DISCONNECTED:
      return _objectSpread(_objectSpread({}, state), {}, {
        websocket: _objectSpread(_objectSpread({}, state.websocket), {}, {
          connected: false,
          connecting: false,
          error: ((_action$payload2 = action.payload) === null || _action$payload2 === void 0 ? void 0 : _action$payload2.error) || null
        })
      });
    case actions/* ActionTypes */.Q3.WS_MESSAGE_RECEIVED:
      // Handle different message types
      var message = action.payload;
      switch (message.type) {
        case 'app_data':
          return _objectSpread(_objectSpread({}, state), {}, {
            websocket: _objectSpread(_objectSpread({}, state.websocket), {}, {
              lastMessage: message
            }),
            app: _objectSpread(_objectSpread({}, state.app), {}, {
              components: message.data.components || [],
              layouts: message.data.layouts || [],
              styles: message.data.styles || {},
              data: message.data.data || {}
            })
          });
        default:
          return _objectSpread(_objectSpread({}, state), {}, {
            websocket: _objectSpread(_objectSpread({}, state.websocket), {}, {
              lastMessage: message
            })
          });
      }
    case actions/* ActionTypes */.Q3.WS_ERROR:
      return _objectSpread(_objectSpread({}, state), {}, {
        websocket: _objectSpread(_objectSpread({}, state.websocket), {}, {
          error: action.payload,
          connected: false,
          connecting: false
        })
      });

    // Component actions
    case actions/* ActionTypes */.Q3.ADD_COMPONENT:
      return _objectSpread(_objectSpread({}, state), {}, {
        app: _objectSpread(_objectSpread({}, state.app), {}, {
          components: [].concat((0,toConsumableArray/* default */.A)(((_state$app = state.app) === null || _state$app === void 0 ? void 0 : _state$app.components) || []), [action.payload])
        })
      });
    case actions/* ActionTypes */.Q3.UPDATE_COMPONENT:
      return _objectSpread(_objectSpread({}, state), {}, {
        app: _objectSpread(_objectSpread({}, state.app), {}, {
          components: (((_state$app2 = state.app) === null || _state$app2 === void 0 ? void 0 : _state$app2.components) || []).map(function (component, index) {
            if (index === action.payload.index) {
              return _objectSpread(_objectSpread({}, component), action.payload.updates);
            }
            return component;
          })
        })
      });
    case actions/* ActionTypes */.Q3.DELETE_COMPONENT:
      return _objectSpread(_objectSpread({}, state), {}, {
        app: _objectSpread(_objectSpread({}, state.app), {}, {
          components: (((_state$app3 = state.app) === null || _state$app3 === void 0 ? void 0 : _state$app3.components) || []).filter(function (_, index) {
            return index !== action.payload.index;
          })
        })
      });

    // Layout actions
    case actions/* ActionTypes */.Q3.ADD_LAYOUT:
      return _objectSpread(_objectSpread({}, state), {}, {
        app: _objectSpread(_objectSpread({}, state.app), {}, {
          layouts: [].concat((0,toConsumableArray/* default */.A)(((_state$app4 = state.app) === null || _state$app4 === void 0 ? void 0 : _state$app4.layouts) || []), [action.payload])
        })
      });
    case actions/* ActionTypes */.Q3.UPDATE_LAYOUT:
      return _objectSpread(_objectSpread({}, state), {}, {
        app: _objectSpread(_objectSpread({}, state.app), {}, {
          layouts: (((_state$app5 = state.app) === null || _state$app5 === void 0 ? void 0 : _state$app5.layouts) || []).map(function (layout, index) {
            if (index === action.payload.index) {
              return _objectSpread(_objectSpread({}, layout), action.payload.updates);
            }
            return layout;
          })
        })
      });
    case actions/* ActionTypes */.Q3.DELETE_LAYOUT:
      return _objectSpread(_objectSpread({}, state), {}, {
        app: _objectSpread(_objectSpread({}, state.app), {}, {
          layouts: (((_state$app6 = state.app) === null || _state$app6 === void 0 ? void 0 : _state$app6.layouts) || []).filter(function (_, index) {
            return index !== action.payload.index;
          })
        })
      });

    // App data actions
    case actions/* ActionTypes */.Q3.SAVE_APP_DATA:
      return _objectSpread(_objectSpread({}, state), {}, {
        app: _objectSpread(_objectSpread({}, state.app), {}, {
          components: action.payload.components || ((_state$app7 = state.app) === null || _state$app7 === void 0 ? void 0 : _state$app7.components) || [],
          layouts: action.payload.layouts || ((_state$app8 = state.app) === null || _state$app8 === void 0 ? void 0 : _state$app8.layouts) || [],
          styles: action.payload.styles || ((_state$app9 = state.app) === null || _state$app9 === void 0 ? void 0 : _state$app9.styles) || {},
          data: action.payload.data || ((_state$app0 = state.app) === null || _state$app0 === void 0 ? void 0 : _state$app0.data) || {}
        })
      });
    case actions/* ActionTypes */.Q3.LOAD_APP_DATA:
      return _objectSpread(_objectSpread({}, state), {}, {
        app: _objectSpread(_objectSpread({}, state.app), {}, {
          components: action.payload.components || [],
          layouts: action.payload.layouts || [],
          styles: action.payload.styles || {},
          data: action.payload.data || {}
        })
      });

    // UI actions
    case actions/* ActionTypes */.Q3.SET_LOADING:
      return _objectSpread(_objectSpread({}, state), {}, {
        loading: action.payload
      });
    case actions/* ActionTypes */.Q3.SET_ERROR:
      return _objectSpread(_objectSpread({}, state), {}, {
        error: action.payload
      });
    case actions/* ActionTypes */.Q3.CLEAR_ERROR:
      return _objectSpread(_objectSpread({}, state), {}, {
        error: null
      });

    // Theme actions
    case 'ADD_THEME':
      return _objectSpread(_objectSpread({}, state), {}, {
        themes: [].concat((0,toConsumableArray/* default */.A)(state.themes), [action.payload])
      });
    case 'UPDATE_THEME':
      return _objectSpread(_objectSpread({}, state), {}, {
        themes: state.themes.map(function (theme) {
          return theme.id === action.payload.id ? action.payload : theme;
        })
      });
    case 'REMOVE_THEME':
      return _objectSpread(_objectSpread({}, state), {}, {
        themes: state.themes.filter(function (theme) {
          return theme.id !== action.payload.id;
        })
      });
    case 'SET_ACTIVE_THEME':
      return _objectSpread(_objectSpread({}, state), {}, {
        activeTheme: action.payload
      });
    default:
      return state;
  }
};
/* harmony default export */ const reducers = (rootReducer);
// EXTERNAL MODULE: ./src/services/WebSocketService.js
var WebSocketService = __webpack_require__(17053);
// EXTERNAL MODULE: ./src/redux/actions/types.js
var actions_types = __webpack_require__(4318);
;// ./src/redux/actions/websocket.actions.js
/**
 * WebSocket Actions
 *
 * This file defines action creators for WebSocket-related actions.
 */



/**
 * Action creator for connecting to WebSocket
 *
 * @returns {Object} Action object
 */
var wsConnect = function wsConnect() {
  return {
    type: types.WS_CONNECT
  };
};

/**
 * Action creator for WebSocket connected event
 *
 * @returns {Object} Action object
 */
var wsConnected = function wsConnected() {
  return {
    type: actions_types/* WS_CONNECTED */.Te
  };
};

/**
 * Action creator for disconnecting from WebSocket
 *
 * @returns {Object} Action object
 */
var wsDisconnect = function wsDisconnect() {
  return {
    type: types.WS_DISCONNECT
  };
};

/**
 * Action creator for WebSocket disconnected event
 *
 * @returns {Object} Action object
 */
var wsDisconnected = function wsDisconnected() {
  return {
    type: actions_types/* WS_DISCONNECTED */.RH
  };
};

/**
 * Action creator for WebSocket error event
 *
 * @param {Error} error - The error that occurred
 * @returns {Object} Action object
 */
var wsError = function wsError(error) {
  return {
    type: actions_types/* WS_ERROR */.AS,
    payload: {
      error: error
    }
  };
};

/**
 * Action creator for sending a message through WebSocket
 *
 * @param {Object} message - The message to send
 * @returns {Object} Action object
 */
var wsSendMessage = function wsSendMessage(message) {
  return {
    type: types.WS_SEND_MESSAGE,
    payload: message
  };
};

/**
 * Action creator for received WebSocket message
 *
 * @param {Object} message - The received message
 * @returns {Object} Action object
 */
var wsMessage = function wsMessage(message) {
  return {
    type: actions_types/* WS_MESSAGE */.H2,
    payload: message
  };
};

/**
 * Action creator for reconnecting to WebSocket
 *
 * @returns {Object} Action object
 */
var wsReconnect = function wsReconnect() {
  return {
    type: types.WS_RECONNECT
  };
};

/**
 * Action creator for clearing WebSocket messages
 *
 * @returns {Object} Action object
 */
var wsClearMessages = function wsClearMessages() {
  return {
    type: types.WS_CLEAR_MESSAGES
  };
};
;// ./src/redux/middleware/websocketMiddleware.js

function websocketMiddleware_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function websocketMiddleware_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? websocketMiddleware_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : websocketMiddleware_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * WebSocket Middleware for Redux
 *
 * This middleware handles WebSocket connections and dispatches actions based on WebSocket events.
 * It also sends messages to the WebSocket server based on specific action types.
 */





/**
 * WebSocket middleware factory
 * @returns {function} Redux middleware
 */
var websocketMiddleware = function websocketMiddleware() {
  // Keep track of the WebSocket connection status
  var isConnected = false;

  // Return the middleware function
  return function (store) {
    return function (next) {
      return function (action) {
        switch (action.type) {
          // Connect to WebSocket
          case actions_types/* WS_CONNECT */.YG:
            if (!isConnected) {
              // Get WebSocket service instance
              var wsService = WebSocketService/* default */.A.getInstance();

              // Initialize WebSocket connection
              wsService.connect().then(function () {
                isConnected = true;
                store.dispatch(wsConnected());
              })["catch"](function (error) {
                store.dispatch(wsError(error));
              });

              // Add event listeners
              wsService.addEventListener('open', function () {
                isConnected = true;
                store.dispatch(wsConnected());
              });
              wsService.addEventListener('close', function () {
                isConnected = false;
                store.dispatch(wsDisconnected());
              });
              wsService.addEventListener('error', function (error) {
                store.dispatch(wsError(error));
              });

              // Add listeners for specific message types
              var messageTypes = ['app_data', 'app_data_update', 'component_added', 'component_updated', 'component_deleted', 'layout_added', 'layout_updated', 'layout_deleted', 'error'];

              // Add message listener for all message types
              wsService.addEventListener('message', function (data) {
                if (data && data.type && messageTypes.includes(data.type)) {
                  // Dispatch a generic message received action
                  store.dispatch(wsMessage({
                    type: data.type,
                    data: data
                  }));

                  // Also dispatch a specific action for this message type
                  store.dispatch({
                    type: "WS_".concat(data.type.toUpperCase()),
                    payload: data
                  });
                }
              });
            }
            break;

          // Disconnect from WebSocket
          case actions_types/* WS_DISCONNECT */.uV:
            if (isConnected) {
              var _wsService = WebSocketService/* default */.A.getInstance();
              _wsService.close();
              isConnected = false;
            }
            break;

          // Send message to WebSocket
          case actions_types/* WS_SEND_MESSAGE */.WD:
            if (isConnected) {
              var _action$payload = action.payload,
                messageType = _action$payload.messageType,
                data = _action$payload.data;
              var _wsService2 = WebSocketService/* default */.A.getInstance();
              _wsService2.sendMessage(websocketMiddleware_objectSpread({
                type: messageType
              }, data))["catch"](function (error) {
                console.error('Error sending message:', error);
                store.dispatch(wsError(error));
              });
            } else {
              console.warn('Cannot send message: WebSocket is not connected');
              store.dispatch(wsError(new Error('WebSocket is not connected')));
            }
            break;
        }

        // Pass the action to the next middleware
        return next(action);
      };
    };
  };
};
/* harmony default export */ const middleware_websocketMiddleware = (websocketMiddleware);
;// ./src/redux/store.js




/**
 * Performance monitoring middleware
 * Tracks the execution time of Redux actions and logs performance metrics
 */
var performanceMiddleware = function performanceMiddleware(store) {
  return function (next) {
    return function (action) {
      // Record the start time
      var start = performance.now();

      // Log the action type and start time (in development only)
      if (false) {}

      // Call the next middleware in the chain
      var result = next(action);

      // Calculate the duration
      var duration = performance.now() - start;

      // Log the action type and duration (in development only)
      if (false) {}

      // Emit a custom event for the performance monitor
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('redux-action', {
          detail: {
            type: action.type,
            duration: duration,
            timestamp: new Date().toISOString(),
            state: store.getState() // Include current state for debugging
          }
        }));
      }
      return result;
    };
  };
};

/**
 * Configure the Redux store
 * Uses Redux Toolkit's configureStore for better defaults and dev tools integration
 */
var createAppStore = function createAppStore() {
  var preloadedState = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  return (0,redux_toolkit_modern/* configureStore */.U1)({
    reducer: reducers,
    preloadedState: preloadedState,
    middleware: function middleware(getDefaultMiddleware) {
      return getDefaultMiddleware({
        serializableCheck: {
          // Ignore these action types in serializability checks
          ignoredActions: ['WS_MESSAGE_RECEIVED', 'WS_ERROR', 'WEBSOCKET_MESSAGE_RECEIVED', 'WEBSOCKET_ERROR'],
          // Ignore these field paths in serializability checks
          ignoredPaths: ['websocket.socket', 'error.originalError', 'webSocketClient.socket']
        },
        thunk: true
      }).concat(middleware_websocketMiddleware(), performanceMiddleware);
    },
    // Enable Redux DevTools integration
    devTools: {
      name: 'App Builder 201',
      trace: true,
      traceLimit: 25
    }
  });
};

// Create the store
var store = createAppStore();

// Export the store and the store creator function
/* harmony default export */ const redux_store = (store);

/***/ }),

/***/ 56702:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PF: () => (/* binding */ LoadingStates),
/* harmony export */   s_: () => (/* binding */ createLazyComponent)
/* harmony export */ });
/* unused harmony exports useProgressiveLoading, preloadOnInteraction, preloadOnVisible, DefaultLoadingComponent, LazyLoadErrorBoundary */
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(58168);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(23029);
/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(92901);
/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(56822);
/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(53954);
/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(85501);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(35346);











function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _callSuper(t, o, e) { return o = (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A)(o), (0,_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }




/**
 * Enhanced Lazy Loading Utilities
 * Provides robust lazy loading infrastructure with error handling and loading states
 */

// Default loading component
var DefaultLoadingComponent = function DefaultLoadingComponent(_ref) {
  var _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'large' : _ref$size,
    _ref$tip = _ref.tip,
    tip = _ref$tip === void 0 ? 'Loading...' : _ref$tip,
    _ref$fullPage = _ref.fullPage,
    fullPage = _ref$fullPage === void 0 ? false : _ref$fullPage,
    _ref$description = _ref.description,
    description = _ref$description === void 0 ? null : _ref$description;
  var spinIcon = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__/* .LoadingOutlined */ .NKq, {
    style: {
      fontSize: 24
    },
    spin: true
  });
  var spinComponent = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement("div", {
    style: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '12px',
      padding: fullPage ? '60px 20px' : '20px',
      minHeight: fullPage ? '200px' : 'auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(antd__WEBPACK_IMPORTED_MODULE_12__/* .Spin */ .tK, {
    indicator: spinIcon,
    size: size,
    tip: tip
  }), description && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement("div", {
    style: {
      color: '#666',
      fontSize: '14px',
      textAlign: 'center',
      maxWidth: '300px'
    }
  }, description));
  return fullPage ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement("div", {
    style: {
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      zIndex: 9999,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }
  }, spinComponent) : spinComponent;
};

// Error boundary for lazy loaded components
var LazyLoadErrorBoundary = /*#__PURE__*/function (_React$Component) {
  function LazyLoadErrorBoundary(props) {
    var _this;
    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(this, LazyLoadErrorBoundary);
    _this = _callSuper(this, LazyLoadErrorBoundary, [props]);
    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A)(_this, "handleRetry", function () {
      _this.setState({
        hasError: false,
        error: null
      });
      // Force a re-render by updating the key
      if (_this.props.onRetry) {
        _this.props.onRetry();
      }
    });
    _this.state = {
      hasError: false,
      error: null
    };
    return _this;
  }
  (0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A)(LazyLoadErrorBoundary, _React$Component);
  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(LazyLoadErrorBoundary, [{
    key: "componentDidCatch",
    value: function componentDidCatch(error, errorInfo) {
      console.error('Lazy loading error:', error, errorInfo);

      // Report to error tracking service if available
      if (window.reportError) {
        window.reportError(error, _objectSpread({
          context: 'lazy-loading'
        }, errorInfo));
      }
    }
  }, {
    key: "render",
    value: function render() {
      if (this.state.hasError) {
        var _this$props = this.props,
          fallback = _this$props.fallback,
          _this$props$component = _this$props.componentName,
          componentName = _this$props$component === void 0 ? 'Component' : _this$props$component;
        if (fallback) {
          return fallback;
        }
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(antd__WEBPACK_IMPORTED_MODULE_12__/* .Alert */ .Fc, {
          message: "Failed to load ".concat(componentName),
          description: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement("p", null, "There was an error loading this component. This might be due to a network issue or a temporary problem."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(antd__WEBPACK_IMPORTED_MODULE_12__/* .Button */ .$n, {
            type: "primary",
            icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__/* .ReloadOutlined */ .KF4, null),
            onClick: this.handleRetry,
            style: {
              marginTop: '8px'
            }
          }, "Try Again")),
          type: "error",
          showIcon: true,
          style: {
            margin: '20px'
          }
        });
      }
      return this.props.children;
    }
  }], [{
    key: "getDerivedStateFromError",
    value: function getDerivedStateFromError(error) {
      return {
        hasError: true,
        error: error
      };
    }
  }]);
}(react__WEBPACK_IMPORTED_MODULE_11__.Component);
/**
 * Enhanced lazy loading wrapper with retry logic and error handling
 */
var createLazyComponent = function createLazyComponent(importFunction) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var _options$fallback = options.fallback,
    fallback = _options$fallback === void 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(DefaultLoadingComponent, null) : _options$fallback,
    _options$errorFallbac = options.errorFallback,
    errorFallback = _options$errorFallbac === void 0 ? null : _options$errorFallbac,
    _options$componentNam = options.componentName,
    componentName = _options$componentNam === void 0 ? 'Component' : _options$componentNam,
    _options$retryAttempt = options.retryAttempts,
    retryAttempts = _options$retryAttempt === void 0 ? 3 : _options$retryAttempt,
    _options$retryDelay = options.retryDelay,
    retryDelay = _options$retryDelay === void 0 ? 1000 : _options$retryDelay,
    _options$preload = options.preload,
    preload = _options$preload === void 0 ? false : _options$preload;

  // Create the lazy component with retry logic
  var LazyComponent = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.lazy(function () {
    var attempts = 0;
    var _loadWithRetry = /*#__PURE__*/function () {
      var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_10___default().mark(function _callee() {
        var module, _t;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_10___default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              _context.next = 1;
              return importFunction();
            case 1:
              module = _context.sent;
              return _context.abrupt("return", module);
            case 2:
              _context.prev = 2;
              _t = _context["catch"](0);
              attempts++;
              if (!(attempts < retryAttempts)) {
                _context.next = 4;
                break;
              }
              console.warn("Failed to load ".concat(componentName, ", retrying... (").concat(attempts, "/").concat(retryAttempts, ")"));
              _context.next = 3;
              return new Promise(function (resolve) {
                return setTimeout(resolve, retryDelay * attempts);
              });
            case 3:
              return _context.abrupt("return", _loadWithRetry());
            case 4:
              console.error("Failed to load ".concat(componentName, " after ").concat(retryAttempts, " attempts:"), _t);
              throw _t;
            case 5:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 2]]);
      }));
      return function loadWithRetry() {
        return _ref2.apply(this, arguments);
      };
    }();
    return _loadWithRetry();
  });

  // Preload the component if requested
  if (preload) {
    // Preload after a short delay to not block initial render
    setTimeout(function () {
      importFunction()["catch"](function (err) {
        console.warn("Preload failed for ".concat(componentName, ":"), err);
      });
    }, 100);
  }

  // Return wrapped component
  var WrappedComponent = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.forwardRef(function (props, ref) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(LazyLoadErrorBoundary, {
      componentName: componentName,
      fallback: errorFallback,
      onRetry: function onRetry() {
        // Force component re-mount by changing key
        if (props.onRetry) props.onRetry();
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(react__WEBPACK_IMPORTED_MODULE_11__.Suspense, {
      fallback: fallback
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(LazyComponent, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)({}, props, {
      ref: ref
    }))));
  });
  WrappedComponent.displayName = "Lazy(".concat(componentName, ")");

  // Add preload method to component
  WrappedComponent.preload = function () {
    return importFunction();
  };
  return WrappedComponent;
};

/**
 * Hook for progressive loading of components
 */
var useProgressiveLoading = function useProgressiveLoading() {
  var components = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
  var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 100;
  var _React$useState = React.useState(new Set()),
    _React$useState2 = _slicedToArray(_React$useState, 2),
    loadedComponents = _React$useState2[0],
    setLoadedComponents = _React$useState2[1];
  React.useEffect(function () {
    var timeouts = [];
    components.forEach(function (component, index) {
      var timeout = setTimeout(function () {
        if (component.preload) {
          component.preload().then(function () {
            setLoadedComponents(function (prev) {
              return new Set([].concat(_toConsumableArray(prev), [component.displayName]));
            });
          })["catch"](function (err) {
            console.warn("Progressive loading failed for ".concat(component.displayName, ":"), err);
          });
        }
      }, delay * (index + 1));
      timeouts.push(timeout);
    });
    return function () {
      timeouts.forEach(clearTimeout);
    };
  }, [components, delay]);
  return loadedComponents;
};

/**
 * Preload components based on user interaction
 */
var preloadOnInteraction = function preloadOnInteraction(component) {
  var events = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : ['mouseenter', 'focus'];
  return function (targetElement) {
    if (!targetElement || !component.preload) return;
    var _handleInteraction = function handleInteraction() {
      component.preload();
      // Remove listeners after first interaction
      events.forEach(function (event) {
        targetElement.removeEventListener(event, _handleInteraction);
      });
    };
    events.forEach(function (event) {
      targetElement.addEventListener(event, _handleInteraction, {
        passive: true
      });
    });
    return function () {
      events.forEach(function (event) {
        targetElement.removeEventListener(event, _handleInteraction);
      });
    };
  };
};

/**
 * Intersection Observer based preloading
 */
var preloadOnVisible = function preloadOnVisible(component) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var _options$threshold = options.threshold,
    threshold = _options$threshold === void 0 ? 0.1 : _options$threshold,
    _options$rootMargin = options.rootMargin,
    rootMargin = _options$rootMargin === void 0 ? '50px' : _options$rootMargin;
  return function (targetElement) {
    if (!targetElement || !component.preload || !window.IntersectionObserver) return;
    var observer = new IntersectionObserver(function (entries) {
      entries.forEach(function (entry) {
        if (entry.isIntersecting) {
          component.preload();
          observer.unobserve(targetElement);
        }
      });
    }, {
      threshold: threshold,
      rootMargin: rootMargin
    });
    observer.observe(targetElement);
    return function () {
      return observer.disconnect();
    };
  };
};

// Export loading components for reuse


// Predefined loading states for common scenarios
var LoadingStates = {
  minimal: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(DefaultLoadingComponent, {
    size: "small",
    tip: "Loading..."
  }),
  standard: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(DefaultLoadingComponent, {
    size: "large",
    tip: "Loading component..."
  }),
  fullPage: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(DefaultLoadingComponent, {
    size: "large",
    tip: "Loading...",
    fullPage: true
  }),
  withDescription: function withDescription(description) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(DefaultLoadingComponent, {
      size: "large",
      tip: "Loading...",
      description: description
    });
  }
};

/***/ }),

/***/ 69477:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  ec: () => (/* binding */ changePassword),
  gf: () => (/* binding */ getToken),
  wz: () => (/* binding */ getUser),
  VM: () => (/* binding */ getUserProfile),
  wR: () => (/* binding */ isAuthenticated),
  iD: () => (/* binding */ login),
  ri: () => (/* binding */ logout),
  Be: () => (/* binding */ refreshToken),
  kz: () => (/* binding */ register),
  eg: () => (/* binding */ updateUserProfile)
});

// UNUSED EXPORTS: clearAuth, getRefreshToken, requestPasswordReset, resetPassword, setRefreshToken, setToken, setUser

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(10467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/regenerator/index.js
var regenerator = __webpack_require__(54756);
var regenerator_default = /*#__PURE__*/__webpack_require__.n(regenerator);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/classCallCheck.js
var classCallCheck = __webpack_require__(23029);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/createClass.js
var createClass = __webpack_require__(92901);
// EXTERNAL MODULE: ./node_modules/axios/index.js + 49 modules
var axios = __webpack_require__(84447);
// EXTERNAL MODULE: ./src/config/api.js
var api = __webpack_require__(81945);
;// ./src/services/csrfService.js




function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

/**
 * CSRF Service
 *
 * This service handles CSRF token management for API requests.
 * It fetches the CSRF token from the backend and provides utilities
 * to include it in requests.
 */


var API_BASE_URL = {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_23400_GEOIWCJYURGQKIDW","COLOR":"0","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_12104_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","JEST_WORKER_ID":"1","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"test","NODE_EXE":"C:\\Program Files\\nodejs\\\\node.exe","NPM_CLI_JS":"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"verbose","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_testpathpattern":"quill-verification","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NPM_PREFIX_JS":"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js","NPM_PREFIX_NPM_CLI_JS":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.1","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_API_BASE_URL || 'http://localhost:8000';
var CSRFService = /*#__PURE__*/function () {
  function CSRFService() {
    (0,classCallCheck/* default */.A)(this, CSRFService);
    this.token = null;
    this.tokenPromise = null;
  }

  /**
   * Get CSRF token from cookie
   * @returns {string|null} CSRF token
   */
  return (0,createClass/* default */.A)(CSRFService, [{
    key: "getTokenFromCookie",
    value: function getTokenFromCookie() {
      var name = 'csrftoken';
      var value = "; ".concat(document.cookie);
      var parts = value.split("; ".concat(name, "="));
      if (parts.length === 2) {
        return parts.pop().split(';').shift();
      }
      return null;
    }

    /**
     * Fetch CSRF token from backend
     * @returns {Promise<string>} CSRF token
     */
  }, {
    key: "fetchToken",
    value: (function () {
      var _fetchToken = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee() {
        var response, data, _t;
        return regenerator_default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              _context.next = 1;
              return fetch(api/* API_ENDPOINTS */.Sn.CSRF_TOKEN, {
                method: 'GET',
                credentials: 'include',
                // Include cookies
                headers: {
                  'Content-Type': 'application/json'
                }
              });
            case 1:
              response = _context.sent;
              if (response.ok) {
                _context.next = 2;
                break;
              }
              throw new Error("Failed to fetch CSRF token: ".concat(response.status));
            case 2:
              _context.next = 3;
              return response.json();
            case 3:
              data = _context.sent;
              this.token = data.csrfToken;
              return _context.abrupt("return", this.token);
            case 4:
              _context.prev = 4;
              _t = _context["catch"](0);
              console.error('Error fetching CSRF token:', _t);
              throw _t;
            case 5:
            case "end":
              return _context.stop();
          }
        }, _callee, this, [[0, 4]]);
      }));
      function fetchToken() {
        return _fetchToken.apply(this, arguments);
      }
      return fetchToken;
    }()
    /**
     * Get CSRF token (from cache, cookie, or fetch from backend)
     * @returns {Promise<string>} CSRF token
     */
    )
  }, {
    key: "getToken",
    value: (function () {
      var _getToken = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2() {
        var cookieToken, token, _t2;
        return regenerator_default().wrap(function (_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              if (!this.token) {
                _context2.next = 1;
                break;
              }
              return _context2.abrupt("return", this.token);
            case 1:
              if (!this.tokenPromise) {
                _context2.next = 2;
                break;
              }
              return _context2.abrupt("return", this.tokenPromise);
            case 2:
              // Try to get token from cookie first
              cookieToken = this.getTokenFromCookie();
              if (!cookieToken) {
                _context2.next = 3;
                break;
              }
              this.token = cookieToken;
              return _context2.abrupt("return", this.token);
            case 3:
              // Fetch token from backend
              this.tokenPromise = this.fetchToken();
              _context2.prev = 4;
              _context2.next = 5;
              return this.tokenPromise;
            case 5:
              token = _context2.sent;
              this.tokenPromise = null;
              return _context2.abrupt("return", token);
            case 6:
              _context2.prev = 6;
              _t2 = _context2["catch"](4);
              this.tokenPromise = null;
              throw _t2;
            case 7:
            case "end":
              return _context2.stop();
          }
        }, _callee2, this, [[4, 6]]);
      }));
      function getToken() {
        return _getToken.apply(this, arguments);
      }
      return getToken;
    }()
    /**
     * Clear cached token
     */
    )
  }, {
    key: "clearToken",
    value: function clearToken() {
      this.token = null;
      this.tokenPromise = null;
    }

    /**
     * Get headers with CSRF token
     * @param {Object} additionalHeaders - Additional headers to include
     * @returns {Promise<Object>} Headers object with CSRF token
     */
  }, {
    key: "getHeaders",
    value: (function () {
      var _getHeaders = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee3() {
        var additionalHeaders,
          token,
          _args3 = arguments,
          _t3;
        return regenerator_default().wrap(function (_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              additionalHeaders = _args3.length > 0 && _args3[0] !== undefined ? _args3[0] : {};
              _context3.prev = 1;
              _context3.next = 2;
              return this.getToken();
            case 2:
              token = _context3.sent;
              return _context3.abrupt("return", _objectSpread({
                'X-CSRFToken': token,
                'Content-Type': 'application/json'
              }, additionalHeaders));
            case 3:
              _context3.prev = 3;
              _t3 = _context3["catch"](1);
              console.warn('Failed to get CSRF token, proceeding without it:', _t3);
              return _context3.abrupt("return", _objectSpread({
                'Content-Type': 'application/json'
              }, additionalHeaders));
            case 4:
            case "end":
              return _context3.stop();
          }
        }, _callee3, this, [[1, 3]]);
      }));
      function getHeaders() {
        return _getHeaders.apply(this, arguments);
      }
      return getHeaders;
    }()
    /**
     * Make a request with CSRF token
     * @param {string} url - Request URL
     * @param {Object} options - Fetch options
     * @returns {Promise<Response>} Fetch response
     */
    )
  }, {
    key: "request",
    value: (function () {
      var _request = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee4(url) {
        var options,
          headers,
          _args4 = arguments;
        return regenerator_default().wrap(function (_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              options = _args4.length > 1 && _args4[1] !== undefined ? _args4[1] : {};
              _context4.next = 1;
              return this.getHeaders(options.headers);
            case 1:
              headers = _context4.sent;
              return _context4.abrupt("return", fetch(url, _objectSpread(_objectSpread({}, options), {}, {
                headers: headers,
                credentials: 'include' // Include cookies
              })));
            case 2:
            case "end":
              return _context4.stop();
          }
        }, _callee4, this);
      }));
      function request(_x) {
        return _request.apply(this, arguments);
      }
      return request;
    }()
    /**
     * Initialize CSRF service by fetching token
     * @returns {Promise<void>}
     */
    )
  }, {
    key: "initialize",
    value: (function () {
      var _initialize = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee5() {
        var _t4;
        return regenerator_default().wrap(function (_context5) {
          while (1) switch (_context5.prev = _context5.next) {
            case 0:
              _context5.prev = 0;
              _context5.next = 1;
              return this.getToken();
            case 1:
              console.log('CSRF service initialized successfully');
              _context5.next = 3;
              break;
            case 2:
              _context5.prev = 2;
              _t4 = _context5["catch"](0);
              console.warn('Failed to initialize CSRF service:', _t4);
            case 3:
            case "end":
              return _context5.stop();
          }
        }, _callee5, this, [[0, 2]]);
      }));
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }())
  }]);
}(); // Create and export singleton instance
var csrfService = new CSRFService();
/* harmony default export */ const services_csrfService = (csrfService);

// Export utility functions
var getCSRFToken = function getCSRFToken() {
  return csrfService.getToken();
};
var getCSRFHeaders = function getCSRFHeaders(additionalHeaders) {
  return csrfService.getHeaders(additionalHeaders);
};
var makeCSRFRequest = function makeCSRFRequest(url, options) {
  return csrfService.request(url, options);
};
var initializeCSRF = function initializeCSRF() {
  return csrfService.initialize();
};
var clearCSRFToken = function clearCSRFToken() {
  return csrfService.clearToken();
};
;// ./src/services/ApiClient.js





function ApiClient_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function ApiClient_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ApiClient_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ApiClient_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * API Client
 * 
 * This service provides methods for making API requests with automatic
 * error handling, retries, and authentication.
 */





// Default configuration
var DEFAULT_CONFIG = {
  baseURL: {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_23400_GEOIWCJYURGQKIDW","COLOR":"0","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_12104_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","JEST_WORKER_ID":"1","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"test","NODE_EXE":"C:\\Program Files\\nodejs\\\\node.exe","NPM_CLI_JS":"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"verbose","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_testpathpattern":"quill-verification","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NPM_PREFIX_JS":"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js","NPM_PREFIX_NPM_CLI_JS":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.1","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_API_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
};
var ApiClient = /*#__PURE__*/function () {
  function ApiClient() {
    var _this = this;
    var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    (0,classCallCheck/* default */.A)(this, ApiClient);
    this.config = ApiClient_objectSpread(ApiClient_objectSpread({}, DEFAULT_CONFIG), config);
    this.client = axios/* default */.Ay.create(this.config);
    this.endpoints = [];
    this.initialized = false;

    // Add request interceptor for authentication and CSRF
    this.client.interceptors.request.use(/*#__PURE__*/function () {
      var _ref = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee(config) {
        var token, csrfHeaders, _t;
        return regenerator_default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              // Add authentication token if available
              token = getToken();
              if (token) {
                config.headers.Authorization = "Bearer ".concat(token);
              }

              // Add CSRF token for state-changing requests
              if (!(config.method && ['post', 'put', 'patch', 'delete'].includes(config.method.toLowerCase()))) {
                _context.next = 4;
                break;
              }
              _context.prev = 1;
              _context.next = 2;
              return services_csrfService.getHeaders();
            case 2:
              csrfHeaders = _context.sent;
              config.headers = ApiClient_objectSpread(ApiClient_objectSpread({}, config.headers), csrfHeaders);
              _context.next = 4;
              break;
            case 3:
              _context.prev = 3;
              _t = _context["catch"](1);
              console.warn('Failed to get CSRF token:', _t);
              if (false) {}
            case 4:
              // Include credentials for CSRF
              config.withCredentials = true;
              return _context.abrupt("return", config);
            case 5:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[1, 3]]);
      }));
      return function (_x) {
        return _ref.apply(this, arguments);
      };
    }(), function (error) {
      return Promise.reject(error);
    });

    // Add response interceptor for CSRF error handling
    this.client.interceptors.response.use(function (response) {
      return response;
    }, /*#__PURE__*/function () {
      var _ref2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2(error) {
        var _error$response, _error$response$data, _error$response$data2;
        var originalRequest, csrfHeaders, _t2;
        return regenerator_default().wrap(function (_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              originalRequest = error.config; // Handle CSRF token errors
              if (!(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 403 && ((_error$response$data = error.response.data) !== null && _error$response$data !== void 0 && (_error$response$data = _error$response$data.message) !== null && _error$response$data !== void 0 && _error$response$data.includes('CSRF') || (_error$response$data2 = error.response.data) !== null && _error$response$data2 !== void 0 && (_error$response$data2 = _error$response$data2.detail) !== null && _error$response$data2 !== void 0 && _error$response$data2.includes('CSRF')) && !originalRequest._retry)) {
                _context2.next = 4;
                break;
              }
              console.warn('CSRF token invalid, clearing and retrying request...');
              originalRequest._retry = true;

              // Clear the invalid token
              services_csrfService.clearToken();

              // Get fresh CSRF token and retry
              _context2.prev = 1;
              _context2.next = 2;
              return services_csrfService.getHeaders();
            case 2:
              csrfHeaders = _context2.sent;
              originalRequest.headers = ApiClient_objectSpread(ApiClient_objectSpread({}, originalRequest.headers), csrfHeaders);
              return _context2.abrupt("return", _this.client(originalRequest));
            case 3:
              _context2.prev = 3;
              _t2 = _context2["catch"](1);
              console.error('Failed to refresh CSRF token:', _t2);
            case 4:
              return _context2.abrupt("return", Promise.reject(error));
            case 5:
            case "end":
              return _context2.stop();
          }
        }, _callee2, null, [[1, 3]]);
      }));
      return function (_x2) {
        return _ref2.apply(this, arguments);
      };
    }());

    // Add response interceptor for handling common errors
    this.client.interceptors.response.use(function (response) {
      return response.data;
    }, /*#__PURE__*/function () {
      var _ref3 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee3(error) {
        var originalRequest, newToken, _t3;
        return regenerator_default().wrap(function (_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              originalRequest = error.config; // Handle token expiration
              if (!(error.response && error.response.status === 401 && !originalRequest._retry)) {
                _context3.next = 5;
                break;
              }
              originalRequest._retry = true;
              _context3.prev = 1;
              _context3.next = 2;
              return refreshToken();
            case 2:
              newToken = _context3.sent;
              if (!newToken) {
                _context3.next = 3;
                break;
              }
              // Update the authorization header
              _this.client.defaults.headers.common.Authorization = "Bearer ".concat(newToken);
              originalRequest.headers.Authorization = "Bearer ".concat(newToken);

              // Retry the original request
              return _context3.abrupt("return", _this.client(originalRequest));
            case 3:
              _context3.next = 5;
              break;
            case 4:
              _context3.prev = 4;
              _t3 = _context3["catch"](1);
              console.error('Token refresh failed:', _t3);

              // Redirect to login page if token refresh fails
              window.location.href = '/login';
              return _context3.abrupt("return", Promise.reject(_t3));
            case 5:
              return _context3.abrupt("return", Promise.reject(error));
            case 6:
            case "end":
              return _context3.stop();
          }
        }, _callee3, null, [[1, 4]]);
      }));
      return function (_x3) {
        return _ref3.apply(this, arguments);
      };
    }());
  }

  /**
   * Initialize the API client
   * @returns {Promise<Object>} Initialization result
   */
  return (0,createClass/* default */.A)(ApiClient, [{
    key: "initServices",
    value: (function () {
      var _initServices = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee4() {
        var _t4;
        return regenerator_default().wrap(function (_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              _context4.prev = 0;
              _context4.next = 1;
              return this.discoverEndpoints();
            case 1:
              this.initialized = true;
              return _context4.abrupt("return", {
                initialized: true,
                endpoints: this.endpoints
              });
            case 2:
              _context4.prev = 2;
              _t4 = _context4["catch"](0);
              console.warn('API client initialization failed:', _t4);

              // In development mode, we can continue with mock data
              if (true) {
                _context4.next = 3;
                break;
              }
              console.log('Development mode: Using mock API');
              this.initialized = true;
              return _context4.abrupt("return", {
                initialized: true,
                mock: true
              });
            case 3:
              throw _t4;
            case 4:
            case "end":
              return _context4.stop();
          }
        }, _callee4, this, [[0, 2]]);
      }));
      function initServices() {
        return _initServices.apply(this, arguments);
      }
      return initServices;
    }()
    /**
     * Discover available API endpoints
     * @returns {Promise<Array>} Available endpoints
     */
    )
  }, {
    key: "discoverEndpoints",
    value: (function () {
      var _discoverEndpoints = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee5() {
        var response, _t5;
        return regenerator_default().wrap(function (_context5) {
          while (1) switch (_context5.prev = _context5.next) {
            case 0:
              _context5.prev = 0;
              _context5.next = 1;
              return this.client.get('/');
            case 1:
              response = _context5.sent;
              if (!(response && response.endpoints)) {
                _context5.next = 2;
                break;
              }
              this.endpoints = response.endpoints;
              return _context5.abrupt("return", this.endpoints);
            case 2:
              // If no endpoints are returned, use default endpoints
              this.endpoints = [{
                path: '/status',
                method: 'GET',
                description: 'Get API status'
              }, {
                path: '/app-data',
                method: 'GET',
                description: 'Get app data'
              }, {
                path: '/components',
                method: 'GET',
                description: 'Get components'
              }, {
                path: '/templates',
                method: 'GET',
                description: 'Get templates'
              }, {
                path: '/projects',
                method: 'GET',
                description: 'Get projects'
              }, {
                path: '/users',
                method: 'GET',
                description: 'Get users'
              }, {
                path: '/auth',
                method: 'POST',
                description: 'Authenticate user'
              }];
              return _context5.abrupt("return", this.endpoints);
            case 3:
              _context5.prev = 3;
              _t5 = _context5["catch"](0);
              console.warn('API endpoint discovery failed:', _t5);

              // Use default endpoints
              this.endpoints = [{
                path: '/status',
                method: 'GET',
                description: 'Get API status'
              }, {
                path: '/app-data',
                method: 'GET',
                description: 'Get app data'
              }, {
                path: '/components',
                method: 'GET',
                description: 'Get components'
              }, {
                path: '/templates',
                method: 'GET',
                description: 'Get templates'
              }, {
                path: '/projects',
                method: 'GET',
                description: 'Get projects'
              }, {
                path: '/users',
                method: 'GET',
                description: 'Get users'
              }, {
                path: '/auth',
                method: 'POST',
                description: 'Authenticate user'
              }];
              return _context5.abrupt("return", this.endpoints);
            case 4:
            case "end":
              return _context5.stop();
          }
        }, _callee5, this, [[0, 3]]);
      }));
      function discoverEndpoints() {
        return _discoverEndpoints.apply(this, arguments);
      }
      return discoverEndpoints;
    }()
    /**
     * Make a GET request
     * @param {string} url - Request URL
     * @param {Object} options - Request options
     * @returns {Promise<Object>} Response data
     */
    )
  }, {
    key: "get",
    value: (function () {
      var _get = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee6(url) {
        var options,
          _args6 = arguments;
        return regenerator_default().wrap(function (_context6) {
          while (1) switch (_context6.prev = _context6.next) {
            case 0:
              options = _args6.length > 1 && _args6[1] !== undefined ? _args6[1] : {};
              return _context6.abrupt("return", this.client.get(url, options));
            case 1:
            case "end":
              return _context6.stop();
          }
        }, _callee6, this);
      }));
      function get(_x4) {
        return _get.apply(this, arguments);
      }
      return get;
    }()
    /**
     * Make a POST request
     * @param {string} url - Request URL
     * @param {Object} data - Request data
     * @param {Object} options - Request options
     * @returns {Promise<Object>} Response data
     */
    )
  }, {
    key: "post",
    value: (function () {
      var _post = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee7(url) {
        var data,
          options,
          _args7 = arguments;
        return regenerator_default().wrap(function (_context7) {
          while (1) switch (_context7.prev = _context7.next) {
            case 0:
              data = _args7.length > 1 && _args7[1] !== undefined ? _args7[1] : {};
              options = _args7.length > 2 && _args7[2] !== undefined ? _args7[2] : {};
              return _context7.abrupt("return", this.client.post(url, data, options));
            case 1:
            case "end":
              return _context7.stop();
          }
        }, _callee7, this);
      }));
      function post(_x5) {
        return _post.apply(this, arguments);
      }
      return post;
    }()
    /**
     * Make a PUT request
     * @param {string} url - Request URL
     * @param {Object} data - Request data
     * @param {Object} options - Request options
     * @returns {Promise<Object>} Response data
     */
    )
  }, {
    key: "put",
    value: (function () {
      var _put = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee8(url) {
        var data,
          options,
          _args8 = arguments;
        return regenerator_default().wrap(function (_context8) {
          while (1) switch (_context8.prev = _context8.next) {
            case 0:
              data = _args8.length > 1 && _args8[1] !== undefined ? _args8[1] : {};
              options = _args8.length > 2 && _args8[2] !== undefined ? _args8[2] : {};
              return _context8.abrupt("return", this.client.put(url, data, options));
            case 1:
            case "end":
              return _context8.stop();
          }
        }, _callee8, this);
      }));
      function put(_x6) {
        return _put.apply(this, arguments);
      }
      return put;
    }()
    /**
     * Make a PATCH request
     * @param {string} url - Request URL
     * @param {Object} data - Request data
     * @param {Object} options - Request options
     * @returns {Promise<Object>} Response data
     */
    )
  }, {
    key: "patch",
    value: (function () {
      var _patch = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee9(url) {
        var data,
          options,
          _args9 = arguments;
        return regenerator_default().wrap(function (_context9) {
          while (1) switch (_context9.prev = _context9.next) {
            case 0:
              data = _args9.length > 1 && _args9[1] !== undefined ? _args9[1] : {};
              options = _args9.length > 2 && _args9[2] !== undefined ? _args9[2] : {};
              return _context9.abrupt("return", this.client.patch(url, data, options));
            case 1:
            case "end":
              return _context9.stop();
          }
        }, _callee9, this);
      }));
      function patch(_x7) {
        return _patch.apply(this, arguments);
      }
      return patch;
    }()
    /**
     * Make a DELETE request
     * @param {string} url - Request URL
     * @param {Object} options - Request options
     * @returns {Promise<Object>} Response data
     */
    )
  }, {
    key: "delete",
    value: (function () {
      var _delete2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee0(url) {
        var options,
          _args0 = arguments;
        return regenerator_default().wrap(function (_context0) {
          while (1) switch (_context0.prev = _context0.next) {
            case 0:
              options = _args0.length > 1 && _args0[1] !== undefined ? _args0[1] : {};
              return _context0.abrupt("return", this.client["delete"](url, options));
            case 1:
            case "end":
              return _context0.stop();
          }
        }, _callee0, this);
      }));
      function _delete(_x8) {
        return _delete2.apply(this, arguments);
      }
      return _delete;
    }()
    /**
     * Check if an endpoint exists
     * @param {string} path - Endpoint path
     * @param {string} method - HTTP method
     * @returns {boolean} Whether the endpoint exists
     */
    )
  }, {
    key: "hasEndpoint",
    value: function hasEndpoint(path) {
      var method = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'GET';
      return this.endpoints.some(function (endpoint) {
        return endpoint.path === path && endpoint.method === method;
      });
    }

    /**
     * Get endpoint description
     * @param {string} path - Endpoint path
     * @param {string} method - HTTP method
     * @returns {string} Endpoint description
     */
  }, {
    key: "getEndpointDescription",
    value: function getEndpointDescription(path) {
      var method = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'GET';
      var endpoint = this.endpoints.find(function (endpoint) {
        return endpoint.path === path && endpoint.method === method;
      });
      return endpoint ? endpoint.description : '';
    }
  }]);
}(); // Create singleton instance
var ApiClient_apiClient = new ApiClient();
/* harmony default export */ const services_ApiClient = (ApiClient_apiClient);
;// ./src/utils/auth.js


/**
 * Authentication Utilities
 * 
 * This module provides utilities for user authentication.
 */



// Local storage keys
var TOKEN_KEY = 'app_auth_token';
var REFRESH_TOKEN_KEY = 'app_refresh_token';
var USER_KEY = 'app_user';

/**
 * Get authentication token
 * @returns {string|null} Authentication token
 */
var getToken = function getToken() {
  return localStorage.getItem(TOKEN_KEY);
};

/**
 * Set authentication token
 * @param {string} token - Authentication token
 */
var setToken = function setToken(token) {
  localStorage.setItem(TOKEN_KEY, token);
};

/**
 * Get refresh token
 * @returns {string|null} Refresh token
 */
var getRefreshToken = function getRefreshToken() {
  return localStorage.getItem(REFRESH_TOKEN_KEY);
};

/**
 * Set refresh token
 * @param {string} token - Refresh token
 */
var setRefreshToken = function setRefreshToken(token) {
  localStorage.setItem(REFRESH_TOKEN_KEY, token);
};

/**
 * Get user data
 * @returns {Object|null} User data
 */
var getUser = function getUser() {
  var userJson = localStorage.getItem(USER_KEY);
  if (userJson) {
    try {
      return JSON.parse(userJson);
    } catch (error) {
      console.error('Error parsing user data:', error);
      return null;
    }
  }
  return null;
};

/**
 * Set user data
 * @param {Object} user - User data
 */
var setUser = function setUser(user) {
  localStorage.setItem(USER_KEY, JSON.stringify(user));
};

/**
 * Clear authentication data
 */
var clearAuth = function clearAuth() {
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(REFRESH_TOKEN_KEY);
  localStorage.removeItem(USER_KEY);
};

/**
 * Check if user is authenticated
 * @returns {boolean} Whether the user is authenticated
 */
var isAuthenticated = function isAuthenticated() {
  return !!getToken();
};

/**
 * Login user
 * @param {string} username - Username
 * @param {string} password - Password
 * @returns {Promise<Object>} Login result
 */
var login = /*#__PURE__*/function () {
  var _ref = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee(username, password) {
    var response, userResponse, _error$response, _t, _t2;
    return regenerator_default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.prev = 0;
          _context.next = 1;
          return services_ApiClient.post('/auth/login/', {
            username: username,
            password: password
          });
        case 1:
          response = _context.sent;
          if (!response.access) {
            _context.next = 6;
            break;
          }
          // JWT response format
          setToken(response.access);
          if (response.refresh) {
            setRefreshToken(response.refresh);
          }

          // Get user profile after successful login
          _context.prev = 2;
          _context.next = 3;
          return services_ApiClient.get('/auth/profile/');
        case 3:
          userResponse = _context.sent;
          if (userResponse) {
            setUser(userResponse);
          }
          _context.next = 5;
          break;
        case 4:
          _context.prev = 4;
          _t = _context["catch"](2);
          console.warn('Failed to fetch user profile:', _t);
        case 5:
          return _context.abrupt("return", {
            success: true,
            user: response.user || {
              username: username
            }
          });
        case 6:
          if (!response.token) {
            _context.next = 7;
            break;
          }
          // Token auth response format
          setToken(response.token);
          if (response.user) {
            setUser(response.user);
          }
          return _context.abrupt("return", {
            success: true,
            user: response.user
          });
        case 7:
          return _context.abrupt("return", {
            success: false,
            error: 'Invalid response from server'
          });
        case 8:
          _context.prev = 8;
          _t2 = _context["catch"](0);
          console.error('Login error:', _t2);
          return _context.abrupt("return", {
            success: false,
            error: ((_error$response = _t2.response) === null || _error$response === void 0 || (_error$response = _error$response.data) === null || _error$response === void 0 ? void 0 : _error$response.detail) || _t2.message || 'Login failed'
          });
        case 9:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[0, 8], [2, 4]]);
  }));
  return function login(_x, _x2) {
    return _ref.apply(this, arguments);
  };
}();

/**
 * Register user
 * @param {Object} userData - User data
 * @returns {Promise<Object>} Registration result
 */
var register = /*#__PURE__*/function () {
  var _ref2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2(userData) {
    var response, userResponse, _error$response2, _error$response3, _t3, _t4;
    return regenerator_default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.prev = 0;
          _context2.next = 1;
          return services_ApiClient.post('/auth/register/', userData);
        case 1:
          response = _context2.sent;
          if (!response.access) {
            _context2.next = 6;
            break;
          }
          // JWT response format
          setToken(response.access);
          if (response.refresh) {
            setRefreshToken(response.refresh);
          }

          // Get user profile after successful registration
          _context2.prev = 2;
          _context2.next = 3;
          return services_ApiClient.get('/auth/profile/');
        case 3:
          userResponse = _context2.sent;
          if (userResponse) {
            setUser(userResponse);
          }
          _context2.next = 5;
          break;
        case 4:
          _context2.prev = 4;
          _t3 = _context2["catch"](2);
          console.warn('Failed to fetch user profile:', _t3);
        case 5:
          return _context2.abrupt("return", {
            success: true,
            user: response.user || {
              username: userData.username
            }
          });
        case 6:
          if (!response.token) {
            _context2.next = 7;
            break;
          }
          // Token auth response format
          setToken(response.token);
          if (response.user) {
            setUser(response.user);
          }
          return _context2.abrupt("return", {
            success: true,
            user: response.user
          });
        case 7:
          if (!response.success) {
            _context2.next = 8;
            break;
          }
          return _context2.abrupt("return", {
            success: true,
            user: response.user
          });
        case 8:
          return _context2.abrupt("return", {
            success: false,
            error: response.error || 'Registration failed'
          });
        case 9:
          _context2.prev = 9;
          _t4 = _context2["catch"](0);
          console.error('Registration error:', _t4);
          return _context2.abrupt("return", {
            success: false,
            error: ((_error$response2 = _t4.response) === null || _error$response2 === void 0 || (_error$response2 = _error$response2.data) === null || _error$response2 === void 0 ? void 0 : _error$response2.detail) || ((_error$response3 = _t4.response) === null || _error$response3 === void 0 || (_error$response3 = _error$response3.data) === null || _error$response3 === void 0 ? void 0 : _error$response3.message) || _t4.message || 'Registration failed'
          });
        case 10:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[0, 9], [2, 4]]);
  }));
  return function register(_x3) {
    return _ref2.apply(this, arguments);
  };
}();

/**
 * Logout user
 * @returns {Promise<Object>} Logout result
 */
var logout = /*#__PURE__*/function () {
  var _ref3 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee3() {
    var _t5;
    return regenerator_default().wrap(function (_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.prev = 0;
          _context3.next = 1;
          return services_ApiClient.post('/auth/logout');
        case 1:
          _context3.next = 3;
          break;
        case 2:
          _context3.prev = 2;
          _t5 = _context3["catch"](0);
          console.warn('Logout notification failed:', _t5);
        case 3:
          _context3.prev = 3;
          // Clear authentication data
          clearAuth();
          return _context3.finish(3);
        case 4:
          return _context3.abrupt("return", {
            success: true
          });
        case 5:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[0, 2, 3, 4]]);
  }));
  return function logout() {
    return _ref3.apply(this, arguments);
  };
}();

/**
 * Refresh authentication token
 * @returns {Promise<string|null>} New authentication token
 */
var refreshToken = /*#__PURE__*/function () {
  var _ref4 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee4() {
    var refreshTokenValue, response, _t6;
    return regenerator_default().wrap(function (_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.prev = 0;
          refreshTokenValue = getRefreshToken();
          if (refreshTokenValue) {
            _context4.next = 1;
            break;
          }
          throw new Error('No refresh token available');
        case 1:
          _context4.next = 2;
          return services_ApiClient.post('/auth/token/refresh/', {
            refresh: refreshTokenValue
          });
        case 2:
          response = _context4.sent;
          if (!response.access) {
            _context4.next = 3;
            break;
          }
          setToken(response.access);
          if (response.refresh) {
            setRefreshToken(response.refresh);
          }
          return _context4.abrupt("return", response.access);
        case 3:
          if (!response.token) {
            _context4.next = 4;
            break;
          }
          // Fallback to token auth format
          setToken(response.token);
          if (response.refreshToken) {
            setRefreshToken(response.refreshToken);
          }
          return _context4.abrupt("return", response.token);
        case 4:
          return _context4.abrupt("return", null);
        case 5:
          _context4.prev = 5;
          _t6 = _context4["catch"](0);
          console.error('Token refresh error:', _t6);

          // Clear authentication data on refresh failure
          clearAuth();
          return _context4.abrupt("return", null);
        case 6:
        case "end":
          return _context4.stop();
      }
    }, _callee4, null, [[0, 5]]);
  }));
  return function refreshToken() {
    return _ref4.apply(this, arguments);
  };
}();

/**
 * Get user profile
 * @returns {Promise<Object>} User profile
 */
var getUserProfile = /*#__PURE__*/function () {
  var _ref5 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee5() {
    var _t7;
    return regenerator_default().wrap(function (_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.prev = 0;
          _context5.next = 1;
          return services_ApiClient.get('/auth/profile');
        case 1:
          return _context5.abrupt("return", _context5.sent);
        case 2:
          _context5.prev = 2;
          _t7 = _context5["catch"](0);
          console.error('Get user profile error:', _t7);
          return _context5.abrupt("return", null);
        case 3:
        case "end":
          return _context5.stop();
      }
    }, _callee5, null, [[0, 2]]);
  }));
  return function getUserProfile() {
    return _ref5.apply(this, arguments);
  };
}();

/**
 * Update user profile
 * @param {Object} profileData - Profile data
 * @returns {Promise<Object>} Update result
 */
var updateUserProfile = /*#__PURE__*/function () {
  var _ref6 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee6(profileData) {
    var response, _t8;
    return regenerator_default().wrap(function (_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.prev = 0;
          _context6.next = 1;
          return services_ApiClient.put('/auth/profile', profileData);
        case 1:
          response = _context6.sent;
          if (!response.user) {
            _context6.next = 2;
            break;
          }
          setUser(response.user);
          return _context6.abrupt("return", {
            success: true,
            user: response.user
          });
        case 2:
          return _context6.abrupt("return", {
            success: false,
            error: 'Invalid response from server'
          });
        case 3:
          _context6.prev = 3;
          _t8 = _context6["catch"](0);
          console.error('Update profile error:', _t8);
          return _context6.abrupt("return", {
            success: false,
            error: _t8.message || 'Update profile failed'
          });
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6, null, [[0, 3]]);
  }));
  return function updateUserProfile(_x4) {
    return _ref6.apply(this, arguments);
  };
}();

/**
 * Change password
 * @param {string} currentPassword - Current password
 * @param {string} newPassword - New password
 * @returns {Promise<Object>} Change result
 */
var changePassword = /*#__PURE__*/function () {
  var _ref7 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee7(currentPassword, newPassword) {
    var response, _t9;
    return regenerator_default().wrap(function (_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.prev = 0;
          _context7.next = 1;
          return services_ApiClient.post('/auth/change-password', {
            currentPassword: currentPassword,
            newPassword: newPassword
          });
        case 1:
          response = _context7.sent;
          return _context7.abrupt("return", {
            success: response.success,
            error: response.error
          });
        case 2:
          _context7.prev = 2;
          _t9 = _context7["catch"](0);
          console.error('Change password error:', _t9);
          return _context7.abrupt("return", {
            success: false,
            error: _t9.message || 'Change password failed'
          });
        case 3:
        case "end":
          return _context7.stop();
      }
    }, _callee7, null, [[0, 2]]);
  }));
  return function changePassword(_x5, _x6) {
    return _ref7.apply(this, arguments);
  };
}();

/**
 * Request password reset
 * @param {string} email - User email
 * @returns {Promise<Object>} Request result
 */
var requestPasswordReset = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref8 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee8(email) {
    var response, _t0;
    return _regeneratorRuntime.wrap(function (_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.prev = 0;
          _context8.next = 1;
          return apiClient.post('/auth/reset-password', {
            email: email
          });
        case 1:
          response = _context8.sent;
          return _context8.abrupt("return", {
            success: response.success,
            error: response.error
          });
        case 2:
          _context8.prev = 2;
          _t0 = _context8["catch"](0);
          console.error('Password reset request error:', _t0);
          return _context8.abrupt("return", {
            success: false,
            error: _t0.message || 'Password reset request failed'
          });
        case 3:
        case "end":
          return _context8.stop();
      }
    }, _callee8, null, [[0, 2]]);
  }));
  return function requestPasswordReset(_x7) {
    return _ref8.apply(this, arguments);
  };
}()));

/**
 * Reset password
 * @param {string} token - Reset token
 * @param {string} newPassword - New password
 * @returns {Promise<Object>} Reset result
 */
var resetPassword = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref9 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee9(token, newPassword) {
    var response, _t1;
    return _regeneratorRuntime.wrap(function (_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.prev = 0;
          _context9.next = 1;
          return apiClient.post('/auth/reset-password/confirm', {
            token: token,
            newPassword: newPassword
          });
        case 1:
          response = _context9.sent;
          return _context9.abrupt("return", {
            success: response.success,
            error: response.error
          });
        case 2:
          _context9.prev = 2;
          _t1 = _context9["catch"](0);
          console.error('Password reset error:', _t1);
          return _context9.abrupt("return", {
            success: false,
            error: _t1.message || 'Password reset failed'
          });
        case 3:
        case "end":
          return _context9.stop();
      }
    }, _callee9, null, [[0, 2]]);
  }));
  return function resetPassword(_x8, _x9) {
    return _ref9.apply(this, arguments);
  };
}()));

/***/ }),

/***/ 77362:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   hl: () => (/* binding */ forceCleanServiceWorkers),
/* harmony export */   jY: () => (/* binding */ fixWebSocketIssues),
/* harmony export */   kz: () => (/* binding */ register)
/* harmony export */ });
/* unused harmony exports unregister, requestNotificationPermission, subscribeToPushNotifications, isAppInstalled, registerBackgroundSync */
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var workbox_window__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(79730);


function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
/**
 * Service Worker Registration with Workbox
 *
 * This file handles the registration of the service worker for the App Builder application.
 * It uses Workbox for advanced caching strategies, offline support, and other PWA features.
 */



// Check if service workers are supported
var isLocalhost = Boolean(window.location.hostname === 'localhost' || window.location.hostname === '[::1]' || window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));

/**
 * Register the service worker using Workbox
 * @param {Object} config - Configuration options
 */
function register() {
  var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  if ('serviceWorker' in navigator) {
    // Check if service worker is temporarily disabled
    try {
      if (localStorage.getItem('disable_sw_temp') === 'true') {
        console.log('Service worker registration skipped: temporarily disabled');
        // Clear the flag after one page load
        localStorage.removeItem('disable_sw_temp');
        return;
      }
    } catch (e) {
      // Ignore localStorage errors
    }

    // The URL constructor is available in all browsers that support SW
    var publicUrl = new URL({"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_23400_GEOIWCJYURGQKIDW","COLOR":"0","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_12104_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","JEST_WORKER_ID":"1","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"test","NODE_EXE":"C:\\Program Files\\nodejs\\\\node.exe","NPM_CLI_JS":"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"verbose","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_testpathpattern":"quill-verification","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NPM_PREFIX_JS":"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js","NPM_PREFIX_NPM_CLI_JS":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.1","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.PUBLIC_URL || '', window.location.href);

    // Our service worker won't work if PUBLIC_URL is on a different origin
    // from what our page is served on. This might happen if a CDN is used.
    if (publicUrl.origin !== window.location.origin) {
      console.log('Service worker registration skipped: different origin');
      return;
    }
    window.addEventListener('load', function () {
      // Use the public service worker for better compatibility
      var swUrl = "".concat({"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_23400_GEOIWCJYURGQKIDW","COLOR":"0","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_12104_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","JEST_WORKER_ID":"1","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"test","NODE_EXE":"C:\\Program Files\\nodejs\\\\node.exe","NPM_CLI_JS":"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"verbose","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_testpathpattern":"quill-verification","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NPM_PREFIX_JS":"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js","NPM_PREFIX_NPM_CLI_JS":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.1","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.PUBLIC_URL || '', "/service-worker.js");

      // Log which service worker we're using
      console.log("Service Worker: Using ".concat(swUrl));

      // Check if WebSocket connections are active before registering
      var hasActiveWebSockets = Array.from(document.querySelectorAll('script')).some(function (script) {
        return script.src && (script.src.includes('socket.io') || script.src.includes('websocket') || script.src.includes('ws'));
      });
      if (hasActiveWebSockets) {
        console.log('Service Worker: Detected active WebSocket scripts, registering with caution');
      }
      if (isLocalhost) {
        // This is running on localhost. Check if a service worker still exists or not.
        checkValidServiceWorker(swUrl, config);

        // Add some additional logging to localhost
        navigator.serviceWorker.ready.then(function () {
          console.log('Service Worker: Ready and running in development mode');
        });
      } else {
        // Not localhost. Register with Workbox
        registerWithWorkbox(swUrl, config);
      }
    });
  } else {
    console.log('Service Worker: Registration skipped - not supported');
  }
}

/**
 * Register the service worker using Workbox
 * @param {string} swUrl - Service worker URL
 * @param {Object} config - Configuration options
 */
function registerWithWorkbox(swUrl, config) {
  try {
    var wb = new workbox_window__WEBPACK_IMPORTED_MODULE_2__/* .Workbox */ .JK(swUrl);

    // Add custom message skip waiting method if it doesn't exist
    if (typeof wb.messageSkipWaiting !== 'function') {
      wb.messageSkipWaiting = function () {
        console.log('Service Worker: Custom messageSkipWaiting called');
        if (wb.controlling) {
          wb.controlling.postMessage('skipWaiting');
        } else {
          navigator.serviceWorker.ready.then(function (registration) {
            if (registration.waiting) {
              registration.waiting.postMessage('skipWaiting');
            }
          });
        }
      };
    }

    // Add event listeners
    wb.addEventListener('installed', function (event) {
      if (event.isUpdate) {
        console.log('Service Worker: Updated service worker installed');
        if (config && config.onUpdate) {
          try {
            config.onUpdate(wb);
          } catch (callbackError) {
            console.error('Service Worker: Error in onUpdate callback:', callbackError);
          }
        }
      } else {
        console.log('Service Worker: New service worker installed');
        if (config && config.onSuccess) {
          try {
            config.onSuccess(wb);
          } catch (callbackError) {
            console.error('Service Worker: Error in onSuccess callback:', callbackError);
          }
        }
      }
    });
    wb.addEventListener('activated', function (event) {
      if (event.isUpdate) {
        console.log('Service Worker: Updated service worker activated');
      } else {
        console.log('Service Worker: New service worker activated');
      }
    });
    wb.addEventListener('waiting', function (event) {
      console.log('Service Worker: New version waiting to be activated');

      // Show update UI if callback provided
      if (config && config.onWaiting) {
        try {
          config.onWaiting(wb);
        } catch (callbackError) {
          console.error('Service Worker: Error in onWaiting callback:', callbackError);
        }
      }
    });
    wb.addEventListener('controlling', function (event) {
      console.log('Service Worker: Controlling the page');
    });
    wb.addEventListener('redundant', function (event) {
      console.warn('Service Worker: The installing service worker became redundant');
    });

    // Add error event listener
    wb.addEventListener('error', function (event) {
      console.error('Service Worker: Error during operation:', event);
    });

    // Register the service worker
    wb.register()["catch"](function (error) {
      console.error('Service Worker: Registration failed:', error);
    });
  } catch (error) {
    console.error('Service Worker: Error during registration:', error);
  }
}

/**
 * Register a valid service worker
 * @param {string} swUrl - Service worker URL
 * @param {Object} config - Configuration options
 */
function registerValidSW(swUrl, config) {
  navigator.serviceWorker.register(swUrl).then(function (registration) {
    // Check for updates on page reload
    registration.onupdatefound = function () {
      var installingWorker = registration.installing;
      if (installingWorker == null) {
        return;
      }
      installingWorker.onstatechange = function () {
        if (installingWorker.state === 'installed') {
          if (navigator.serviceWorker.controller) {
            // At this point, the updated precached content has been fetched,
            // but the previous service worker will still serve the older content
            console.log('Service Worker: New content is available and will be used when all tabs for this page are closed');

            // Execute callback if provided
            if (config && config.onUpdate) {
              config.onUpdate(registration);
            }
          } else {
            // At this point, everything has been precached.
            console.log('Service Worker: Content is cached for offline use');

            // Execute callback if provided
            if (config && config.onSuccess) {
              config.onSuccess(registration);
            }
          }
        }
      };
    };
  })["catch"](function (error) {
    console.error('Service Worker: Error during registration:', error);
  });
}

/**
 * Check if a service worker is valid
 * @param {string} swUrl - Service worker URL
 * @param {Object} config - Configuration options
 */
function checkValidServiceWorker(swUrl, config) {
  // Check if the service worker can be found
  fetch(swUrl, {
    headers: {
      'Service-Worker': 'script'
    },
    // Add cache: 'reload' to avoid getting a cached version
    cache: 'reload'
  }).then(function (response) {
    // Ensure service worker exists, and that we really are getting a JS file
    var contentType = response.headers.get('content-type');
    if (response.status === 404 || contentType != null && contentType.indexOf('javascript') === -1) {
      console.warn('Service Worker: Invalid service worker detected. Attempting to unregister.');
      // No service worker found. Probably a different app. Reload the page.
      navigator.serviceWorker.getRegistrations().then(function (registrations) {
        var _iterator = _createForOfIteratorHelper(registrations),
          _step;
        try {
          for (_iterator.s(); !(_step = _iterator.n()).done;) {
            var registration = _step.value;
            console.log('Unregistering service worker:', registration.scope);
            registration.unregister();
          }
          // Reload after unregistering all service workers
        } catch (err) {
          _iterator.e(err);
        } finally {
          _iterator.f();
        }
        window.location.reload();
      });
    } else {
      // Service worker found. Proceed as normal.
      console.log('Service Worker: Valid service worker found. Registering...');
      registerValidSW(swUrl, config);
    }
  })["catch"](function (error) {
    console.log('Service Worker: No internet connection or error occurred:', error);
    console.log('App is running in offline mode.');
  });
}

/**
 * Unregister the service worker
 */
function unregister() {
  if ('serviceWorker' in navigator) {
    // Unregister all service workers, not just the current one
    navigator.serviceWorker.getRegistrations().then(function (registrations) {
      var _iterator2 = _createForOfIteratorHelper(registrations),
        _step2;
      try {
        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
          var registration = _step2.value;
          console.log('Unregistering service worker:', registration.scope);
          registration.unregister();
        }
      } catch (err) {
        _iterator2.e(err);
      } finally {
        _iterator2.f();
      }
      console.log('All service workers unregistered');
    })["catch"](function (error) {
      console.error('Service Worker: Error during unregistration:', error);
    });
  }
}

// forceCleanServiceWorkers is now defined below with a Promise-based API

/**
 * Force unregister all service workers and clear caches
 * @returns {Promise<boolean>} - True if any service workers were unregistered
 */
function forceCleanServiceWorkers() {
  return _forceCleanServiceWorkers.apply(this, arguments);
}

/**
 * Fix WebSocket connection issues by unregistering service workers
 * that might be intercepting WebSocket connections
 * @returns {Promise<boolean>} - Promise that resolves to true if issues were fixed
 */
function _forceCleanServiceWorkers() {
  _forceCleanServiceWorkers = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee2() {
    var registrations, _iterator5, _step5, registration, cacheNames, _iterator6, _step6, cacheName, _t5, _t6, _t7, _t8, _t9;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          if ('serviceWorker' in navigator) {
            _context2.next = 1;
            break;
          }
          return _context2.abrupt("return", false);
        case 1:
          _context2.prev = 1;
          _context2.next = 2;
          return navigator.serviceWorker.getRegistrations();
        case 2:
          registrations = _context2.sent;
          if (!(registrations.length === 0)) {
            _context2.next = 3;
            break;
          }
          console.log('No service workers to clean');
          return _context2.abrupt("return", false);
        case 3:
          console.log("Forcefully cleaning ".concat(registrations.length, " service workers"));

          // Unregister all service workers
          _iterator5 = _createForOfIteratorHelper(registrations);
          _context2.prev = 4;
          _iterator5.s();
        case 5:
          if ((_step5 = _iterator5.n()).done) {
            _context2.next = 10;
            break;
          }
          registration = _step5.value;
          _context2.prev = 6;
          _context2.next = 7;
          return registration.unregister();
        case 7:
          console.log('Unregistered service worker:', registration.scope);
          _context2.next = 9;
          break;
        case 8:
          _context2.prev = 8;
          _t5 = _context2["catch"](6);
          console.error('Failed to unregister service worker:', _t5);
        case 9:
          _context2.next = 5;
          break;
        case 10:
          _context2.next = 12;
          break;
        case 11:
          _context2.prev = 11;
          _t6 = _context2["catch"](4);
          _iterator5.e(_t6);
        case 12:
          _context2.prev = 12;
          _iterator5.f();
          return _context2.finish(12);
        case 13:
          if (!('caches' in window)) {
            _context2.next = 25;
            break;
          }
          _context2.prev = 14;
          _context2.next = 15;
          return caches.keys();
        case 15:
          cacheNames = _context2.sent;
          _iterator6 = _createForOfIteratorHelper(cacheNames);
          _context2.prev = 16;
          _iterator6.s();
        case 17:
          if ((_step6 = _iterator6.n()).done) {
            _context2.next = 20;
            break;
          }
          cacheName = _step6.value;
          _context2.next = 18;
          return caches["delete"](cacheName);
        case 18:
          console.log('Deleted cache:', cacheName);
        case 19:
          _context2.next = 17;
          break;
        case 20:
          _context2.next = 22;
          break;
        case 21:
          _context2.prev = 21;
          _t7 = _context2["catch"](16);
          _iterator6.e(_t7);
        case 22:
          _context2.prev = 22;
          _iterator6.f();
          return _context2.finish(22);
        case 23:
          _context2.next = 25;
          break;
        case 24:
          _context2.prev = 24;
          _t8 = _context2["catch"](14);
          console.error('Failed to clear caches:', _t8);
        case 25:
          // Set flag to prevent service worker registration on next load
          try {
            localStorage.setItem('disable_sw_temp', 'true');
          } catch (e) {
            // Ignore localStorage errors
          }
          return _context2.abrupt("return", true);
        case 26:
          _context2.prev = 26;
          _t9 = _context2["catch"](1);
          console.error('Error cleaning service workers:', _t9);
          return _context2.abrupt("return", false);
        case 27:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[1, 26], [4, 11, 12, 13], [6, 8], [14, 24], [16, 21, 22, 23]]);
  }));
  return _forceCleanServiceWorkers.apply(this, arguments);
}
function fixWebSocketIssues() {
  if ('serviceWorker' in navigator) {
    console.log('Fixing WebSocket issues by checking service workers...');
    return new Promise(function (resolve) {
      // Check if there are any active service workers
      navigator.serviceWorker.getRegistrations().then(/*#__PURE__*/function () {
        var _ref = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee(registrations) {
          var _iterator3, _step3, registration, cacheNames, _iterator4, _step4, cacheName, _t, _t2, _t3, _t4;
          return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().wrap(function (_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                if (!(registrations.length > 0)) {
                  _context.next = 22;
                  break;
                }
                console.log("Found ".concat(registrations.length, " service worker registrations that might affect WebSockets"));

                // Unregister service workers that might be causing issues
                _iterator3 = _createForOfIteratorHelper(registrations);
                _context.prev = 1;
                _iterator3.s();
              case 2:
                if ((_step3 = _iterator3.n()).done) {
                  _context.next = 7;
                  break;
                }
                registration = _step3.value;
                console.log('Unregistering service worker to fix WebSocket issues:', registration.scope);
                _context.prev = 3;
                _context.next = 4;
                return registration.unregister();
              case 4:
                console.log('Successfully unregistered service worker');
                _context.next = 6;
                break;
              case 5:
                _context.prev = 5;
                _t = _context["catch"](3);
                console.error('Failed to unregister service worker:', _t);
              case 6:
                _context.next = 2;
                break;
              case 7:
                _context.next = 9;
                break;
              case 8:
                _context.prev = 8;
                _t2 = _context["catch"](1);
                _iterator3.e(_t2);
              case 9:
                _context.prev = 9;
                _iterator3.f();
                return _context.finish(9);
              case 10:
                if (!('caches' in window)) {
                  _context.next = 21;
                  break;
                }
                _context.prev = 11;
                _context.next = 12;
                return caches.keys();
              case 12:
                cacheNames = _context.sent;
                _iterator4 = _createForOfIteratorHelper(cacheNames);
                _context.prev = 13;
                _iterator4.s();
              case 14:
                if ((_step4 = _iterator4.n()).done) {
                  _context.next = 16;
                  break;
                }
                cacheName = _step4.value;
                if (!(cacheName.includes('api') || cacheName.includes('ws') || cacheName.includes('socket') || cacheName.includes('workbox'))) {
                  _context.next = 15;
                  break;
                }
                console.log('Deleting potentially problematic cache:', cacheName);
                _context.next = 15;
                return caches["delete"](cacheName);
              case 15:
                _context.next = 14;
                break;
              case 16:
                _context.next = 18;
                break;
              case 17:
                _context.prev = 17;
                _t3 = _context["catch"](13);
                _iterator4.e(_t3);
              case 18:
                _context.prev = 18;
                _iterator4.f();
                return _context.finish(18);
              case 19:
                console.log('Cache cleanup completed');
                _context.next = 21;
                break;
              case 20:
                _context.prev = 20;
                _t4 = _context["catch"](11);
                console.error('Error cleaning caches:', _t4);
              case 21:
                // Add a special flag to localStorage to prevent service worker registration on next load
                try {
                  localStorage.setItem('disable_sw_temp', 'true');
                  console.log('Temporarily disabled service worker for next page load');
                } catch (storageError) {
                  console.error('Failed to set localStorage flag:', storageError);
                }
                console.log('WebSocket issues should be fixed. Reloading page...');
                // Reload the page to ensure clean state
                setTimeout(function () {
                  window.location.reload();
                }, 1000);
                resolve(true);
                _context.next = 23;
                break;
              case 22:
                console.log('No service workers found that might affect WebSockets');
                resolve(false);
              case 23:
              case "end":
                return _context.stop();
            }
          }, _callee, null, [[1, 8, 9, 10], [3, 5], [11, 20], [13, 17, 18, 19]]);
        }));
        return function (_x) {
          return _ref.apply(this, arguments);
        };
      }())["catch"](function (error) {
        console.error('Error while fixing WebSocket issues:', error);
        resolve(false);
      });
    });
  }
  return Promise.resolve(false);
}

/**
 * Request notification permission
 * @returns {Promise} - Promise that resolves with the permission status
 */
function requestNotificationPermission() {
  if (!('Notification' in window)) {
    console.log('This browser does not support notifications');
    return Promise.resolve('denied');
  }
  if (Notification.permission === 'granted') {
    return Promise.resolve('granted');
  }
  if (Notification.permission === 'denied') {
    console.log('Notification permission denied');
    return Promise.resolve('denied');
  }
  return Notification.requestPermission();
}

/**
 * Subscribe to push notifications
 * @param {string} applicationServerKey - VAPID public key
 * @returns {Promise} - Promise that resolves with the subscription
 */
function subscribeToPushNotifications(applicationServerKey) {
  if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
    console.log('Push notifications are not supported');
    return Promise.resolve(null);
  }
  return navigator.serviceWorker.ready.then(function (registration) {
    return registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: applicationServerKey
    });
  }).then(function (subscription) {
    console.log('User is subscribed to push notifications');
    return subscription;
  })["catch"](function (error) {
    console.error('Failed to subscribe to push notifications:', error);
    return null;
  });
}

/**
 * Check if the app is installed (in standalone mode)
 * @returns {boolean} - True if the app is installed
 */
function isAppInstalled() {
  return window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone === true;
}

/**
 * Register for background sync
 * @param {string} tag - Sync tag
 * @returns {Promise} - Promise that resolves when sync is registered
 */
function registerBackgroundSync(tag) {
  if (!('serviceWorker' in navigator) || !('SyncManager' in window)) {
    console.log('Background sync is not supported');
    return Promise.resolve(false);
  }
  return navigator.serviceWorker.ready.then(function (registration) {
    return registration.sync.register(tag);
  }).then(function () {
    console.log("Background sync registered for ".concat(tag));
    return true;
  })["catch"](function (error) {
    console.error('Failed to register background sync:', error);
    return false;
  });
}

/***/ }),

/***/ 81616:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $5: () => (/* binding */ deleteComponent),
/* harmony export */   Ic: () => (/* binding */ setActiveTheme),
/* harmony export */   Q3: () => (/* binding */ ActionTypes),
/* harmony export */   Qo: () => (/* binding */ removeTheme),
/* harmony export */   RT: () => (/* binding */ loadProjects),
/* harmony export */   S7: () => (/* binding */ addLayout),
/* harmony export */   V_: () => (/* binding */ updateTheme),
/* harmony export */   X8: () => (/* binding */ addComponent),
/* harmony export */   ZL: () => (/* binding */ saveAppData),
/* harmony export */   ZP: () => (/* binding */ updateComponent),
/* harmony export */   eL: () => (/* binding */ setActiveProject),
/* harmony export */   gA: () => (/* binding */ createProject),
/* harmony export */   vr: () => (/* binding */ updateProject),
/* harmony export */   xx: () => (/* binding */ deleteProject),
/* harmony export */   zp: () => (/* binding */ addTheme)
/* harmony export */ });
/* unused harmony exports ADD_COMPONENT, ADD_LAYOUT, ADD_STYLE, ADD_DATA, FETCH_APP_DATA_SUCCESS, FETCH_APP_DATA_ERROR, WS_CONNECT, WS_CONNECTED, WS_DISCONNECTED, WS_MESSAGE_RECEIVED, WS_ERROR, UPDATE_COMPONENT, DELETE_COMPONENT, UPDATE_LAYOUT, DELETE_LAYOUT, SAVE_APP_DATA, LOAD_APP_DATA, SET_LOADING, SET_ERROR, CLEAR_ERROR, addStyle, addData, fetchAppDataSuccess, fetchAppDataError, fetchAppData, updateLayout, removeLayout, setLoading, setError, clearError, setUser, wsConnect, wsConnected, wsDisconnected, wsMessageReceived, wsError */
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1__);


// Action types
var ADD_COMPONENT = 'ADD_COMPONENT';
var ADD_LAYOUT = 'ADD_LAYOUT';
var ADD_STYLE = 'ADD_STYLE';
var ADD_DATA = 'ADD_DATA';
var FETCH_APP_DATA_SUCCESS = 'FETCH_APP_DATA_SUCCESS';
var FETCH_APP_DATA_ERROR = 'FETCH_APP_DATA_ERROR';

// Additional action types used in reducers
var WS_CONNECT = 'WS_CONNECT';
var WS_CONNECTED = 'WS_CONNECTED';
var WS_DISCONNECTED = 'WS_DISCONNECTED';
var WS_MESSAGE_RECEIVED = 'WS_MESSAGE_RECEIVED';
var WS_ERROR = 'WS_ERROR';
var UPDATE_COMPONENT = 'UPDATE_COMPONENT';
var DELETE_COMPONENT = 'DELETE_COMPONENT';
var UPDATE_LAYOUT = 'UPDATE_LAYOUT';
var DELETE_LAYOUT = 'DELETE_LAYOUT';
var SAVE_APP_DATA = 'SAVE_APP_DATA';
var LOAD_APP_DATA = 'LOAD_APP_DATA';
var SET_LOADING = 'SET_LOADING';
var SET_ERROR = 'SET_ERROR';
var CLEAR_ERROR = 'CLEAR_ERROR';

// ActionTypes object for compatibility with reducers
var ActionTypes = {
  ADD_COMPONENT: ADD_COMPONENT,
  ADD_LAYOUT: ADD_LAYOUT,
  ADD_STYLE: ADD_STYLE,
  ADD_DATA: ADD_DATA,
  FETCH_APP_DATA_SUCCESS: FETCH_APP_DATA_SUCCESS,
  FETCH_APP_DATA_ERROR: FETCH_APP_DATA_ERROR,
  WS_CONNECT: WS_CONNECT,
  WS_CONNECTED: WS_CONNECTED,
  WS_DISCONNECTED: WS_DISCONNECTED,
  WS_MESSAGE_RECEIVED: WS_MESSAGE_RECEIVED,
  WS_ERROR: WS_ERROR,
  UPDATE_COMPONENT: UPDATE_COMPONENT,
  DELETE_COMPONENT: DELETE_COMPONENT,
  UPDATE_LAYOUT: UPDATE_LAYOUT,
  DELETE_LAYOUT: DELETE_LAYOUT,
  SAVE_APP_DATA: SAVE_APP_DATA,
  LOAD_APP_DATA: LOAD_APP_DATA,
  SET_LOADING: SET_LOADING,
  SET_ERROR: SET_ERROR,
  CLEAR_ERROR: CLEAR_ERROR
};

// Action creators
var addComponent = function addComponent(component) {
  return {
    type: ADD_COMPONENT,
    payload: component
  };
};
var addLayout = function addLayout(layoutType) {
  var components = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
  var styles = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  return {
    type: ADD_LAYOUT,
    payload: {
      type: layoutType,
      components: components,
      styles: styles
    }
  };
};
var addStyle = function addStyle(selector, style) {
  return {
    type: ADD_STYLE,
    payload: {
      selector: selector,
      style: style
    }
  };
};
var addData = function addData(key, value) {
  return {
    type: ADD_DATA,
    payload: {
      key: key,
      value: value
    }
  };
};
var fetchAppDataSuccess = function fetchAppDataSuccess(data) {
  return {
    type: FETCH_APP_DATA_SUCCESS,
    payload: data
  };
};
var fetchAppDataError = function fetchAppDataError(error) {
  return {
    type: FETCH_APP_DATA_ERROR,
    payload: error
  };
};

// API Base URL
var API_BASE_URL = {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_23400_GEOIWCJYURGQKIDW","COLOR":"0","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_12104_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","JEST_WORKER_ID":"1","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"test","NODE_EXE":"C:\\Program Files\\nodejs\\\\node.exe","NPM_CLI_JS":"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"verbose","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_testpathpattern":"quill-verification","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NPM_PREFIX_JS":"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js","NPM_PREFIX_NPM_CLI_JS":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.1","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_API_BASE_URL || 'http://localhost:8000';

// Async action creators
var fetchAppData = function fetchAppData() {
  return /*#__PURE__*/function () {
    var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee(dispatch) {
      var response, data, _t;
      return _regeneratorRuntime.wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            _context.next = 1;
            return fetch("".concat(API_BASE_URL, "/api/app-data/"));
          case 1:
            response = _context.sent;
            _context.next = 2;
            return response.json();
          case 2:
            data = _context.sent;
            dispatch(fetchAppDataSuccess(data));
            _context.next = 4;
            break;
          case 3:
            _context.prev = 3;
            _t = _context["catch"](0);
            console.error('Error fetching app data:', _t);
            dispatch(fetchAppDataError(_t.message));
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 3]]);
    }));
    return function (_x) {
      return _ref.apply(this, arguments);
    };
  }();
};
var saveAppData = function saveAppData(appData) {
  return /*#__PURE__*/function () {
    var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee2(dispatch) {
      var response, data, _t2;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            _context2.next = 1;
            return fetch("".concat(API_BASE_URL, "/api/app-data/"), {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(appData)
            });
          case 1:
            response = _context2.sent;
            _context2.next = 2;
            return response.json();
          case 2:
            data = _context2.sent;
            dispatch(fetchAppDataSuccess(data));
            _context2.next = 4;
            break;
          case 3:
            _context2.prev = 3;
            _t2 = _context2["catch"](0);
            console.error('Error saving app data:', _t2);
            dispatch(fetchAppDataError(_t2.message));
          case 4:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 3]]);
    }));
    return function (_x2) {
      return _ref2.apply(this, arguments);
    };
  }();
};

// Additional action creators for missing functions
var updateComponent = function updateComponent(index, updates) {
  return {
    type: UPDATE_COMPONENT,
    payload: {
      index: index,
      updates: updates
    }
  };
};
var deleteComponent = function deleteComponent(index) {
  return {
    type: DELETE_COMPONENT,
    payload: {
      index: index
    }
  };
};
var updateLayout = function updateLayout(index, updates) {
  return {
    type: UPDATE_LAYOUT,
    payload: {
      index: index,
      updates: updates
    }
  };
};
var removeLayout = function removeLayout(index) {
  return {
    type: DELETE_LAYOUT,
    payload: {
      index: index
    }
  };
};
var setLoading = function setLoading(loading) {
  return {
    type: SET_LOADING,
    payload: loading
  };
};
var setError = function setError(error) {
  return {
    type: SET_ERROR,
    payload: error
  };
};
var clearError = function clearError() {
  return {
    type: CLEAR_ERROR
  };
};

// Project management actions
var loadProjects = function loadProjects() {
  return {
    type: 'LOAD_PROJECTS'
  };
};
var setActiveProject = function setActiveProject(projectId) {
  return {
    type: 'SET_ACTIVE_PROJECT',
    payload: projectId
  };
};
var updateProject = function updateProject(project) {
  return {
    type: 'UPDATE_PROJECT',
    payload: project
  };
};
var createProject = function createProject(project) {
  return {
    type: 'CREATE_PROJECT',
    payload: project
  };
};
var deleteProject = function deleteProject(projectId) {
  return {
    type: 'DELETE_PROJECT',
    payload: projectId
  };
};

// Theme management actions
var addTheme = function addTheme(theme) {
  return {
    type: 'ADD_THEME',
    payload: theme
  };
};
var updateTheme = function updateTheme(theme) {
  return {
    type: 'UPDATE_THEME',
    payload: theme
  };
};
var removeTheme = function removeTheme(themeId) {
  return {
    type: 'REMOVE_THEME',
    payload: {
      id: themeId
    }
  };
};
var setActiveTheme = function setActiveTheme(themeId) {
  return {
    type: 'SET_ACTIVE_THEME',
    payload: themeId
  };
};

// User management actions
var setUser = function setUser(user) {
  return {
    type: 'SET_USER',
    payload: user
  };
};

// WebSocket action creators
var wsConnect = function wsConnect(url) {
  return {
    type: WS_CONNECT,
    payload: {
      url: url
    }
  };
};
var wsConnected = function wsConnected() {
  return {
    type: WS_CONNECTED
  };
};
var wsDisconnected = function wsDisconnected() {
  var error = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
  return {
    type: WS_DISCONNECTED,
    payload: {
      error: error
    }
  };
};
var wsMessageReceived = function wsMessageReceived(message) {
  return {
    type: WS_MESSAGE_RECEIVED,
    payload: message
  };
};
var wsError = function wsError(error) {
  return {
    type: WS_ERROR,
    payload: error
  };
};

/***/ }),

/***/ 93590:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {


// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/react-dom/client.js
var client = __webpack_require__(5338);
// EXTERNAL MODULE: ./node_modules/react-redux/dist/react-redux.mjs
var react_redux = __webpack_require__(71468);
// EXTERNAL MODULE: ./src/redux/store.js + 3 modules
var store = __webpack_require__(48035);
// EXTERNAL MODULE: ./node_modules/react-router-dom/dist/index.js + 1 modules
var dist = __webpack_require__(11080);
// EXTERNAL MODULE: ./src/Routes.js + 7 modules
var Routes = __webpack_require__(86801);
// EXTERNAL MODULE: ./src/contexts/AuthContext.js + 1 modules
var AuthContext = __webpack_require__(49391);
// EXTERNAL MODULE: ./src/components/theme/ThemeManager.js
var ThemeManager = __webpack_require__(93385);
// EXTERNAL MODULE: ./src/components/common/EnhancedErrorBoundary.js
var EnhancedErrorBoundary = __webpack_require__(30403);
// EXTERNAL MODULE: ./src/serviceWorkerRegistration.js
var serviceWorkerRegistration = __webpack_require__(77362);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/classCallCheck.js
var classCallCheck = __webpack_require__(23029);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/createClass.js
var createClass = __webpack_require__(92901);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js
var possibleConstructorReturn = __webpack_require__(56822);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js
var getPrototypeOf = __webpack_require__(53954);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/inherits.js
var inherits = __webpack_require__(85501);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/wrapNativeSuper.js + 2 modules
var wrapNativeSuper = __webpack_require__(73437);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
;// ./src/utils/mockWebSocketServer.js







function _callSuper(t, o, e) { return o = (0,getPrototypeOf/* default */.A)(o), (0,possibleConstructorReturn/* default */.A)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0,getPrototypeOf/* default */.A)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
/**
 * Mock WebSocket Server
 * 
 * This module provides a mock WebSocket server for development and testing.
 * It simulates WebSocket connections and messages when the backend is not available.
 */
// Mock WebSocket class
var MockWebSocket = /*#__PURE__*/function (_EventTarget) {
  function MockWebSocket(url) {
    var _this;
    (0,classCallCheck/* default */.A)(this, MockWebSocket);
    _this = _callSuper(this, MockWebSocket);
    _this.url = url;
    _this.readyState = MockWebSocket.CONNECTING;
    _this.protocol = '';
    _this.extensions = '';
    _this.bufferedAmount = 0;
    _this.binaryType = 'blob';

    // Simulate connection delay
    setTimeout(function () {
      _this.readyState = MockWebSocket.OPEN;

      // Dispatch open event
      var openEvent = new Event('open');
      _this.dispatchEvent(openEvent);
      if (typeof _this.onopen === 'function') {
        _this.onopen(openEvent);
      }
      console.log("Mock WebSocket connected to ".concat(url));
    }, 500);
    return _this;
  }

  // Send method
  (0,inherits/* default */.A)(MockWebSocket, _EventTarget);
  return (0,createClass/* default */.A)(MockWebSocket, [{
    key: "send",
    value: function send(data) {
      var _this2 = this;
      if (this.readyState !== MockWebSocket.OPEN) {
        throw new Error('WebSocket is not open');
      }
      console.log("Mock WebSocket sending data:", data);

      // Parse the data
      var parsedData;
      try {
        parsedData = typeof data === 'string' ? JSON.parse(data) : data;
      } catch (error) {
        parsedData = data;
      }

      // Handle different message types
      setTimeout(function () {
        if (parsedData.type === 'ping') {
          _this2._handlePing(parsedData);
        } else if (parsedData.type === 'request_app_data') {
          _this2._handleAppDataRequest(parsedData);
        } else {
          _this2._handleGenericMessage(parsedData);
        }
      }, 200);
    }

    // Close method
  }, {
    key: "close",
    value: function close() {
      var _this3 = this;
      var code = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1000;
      var reason = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
      if (this.readyState === MockWebSocket.CLOSED) {
        return;
      }
      this.readyState = MockWebSocket.CLOSING;

      // Simulate closing delay
      setTimeout(function () {
        _this3.readyState = MockWebSocket.CLOSED;

        // Dispatch close event
        var closeEvent = new CloseEvent('close', {
          code: code,
          reason: reason,
          wasClean: code === 1000
        });
        _this3.dispatchEvent(closeEvent);
        if (typeof _this3.onclose === 'function') {
          _this3.onclose(closeEvent);
        }
        console.log("Mock WebSocket closed: ".concat(code, " ").concat(reason));
      }, 100);
    }

    // Handle ping message
  }, {
    key: "_handlePing",
    value: function _handlePing(data) {
      // Respond with pong
      var response = {
        type: 'pong',
        timestamp: new Date().toISOString(),
        originalTimestamp: data.timestamp,
        server: 'MockWebSocketServer'
      };
      this._sendMessage(response);
    }

    // Handle app data request
  }, {
    key: "_handleAppDataRequest",
    value: function _handleAppDataRequest(data) {
      // Respond with mock app data
      var response = {
        type: 'app_data',
        data: {
          app: {
            name: 'App Builder',
            version: '1.0.0',
            components: [{
              id: 1,
              type: 'Button',
              props: {
                text: 'Click Me',
                variant: 'primary'
              }
            }, {
              id: 2,
              type: 'Input',
              props: {
                placeholder: 'Enter text',
                label: 'Name'
              }
            }, {
              id: 3,
              type: 'Text',
              props: {
                content: 'Hello World',
                style: {
                  fontWeight: 'bold'
                }
              }
            }],
            layouts: [{
              id: 1,
              type: 'Grid',
              components: [1, 2],
              styles: {
                gap: '10px'
              }
            }, {
              id: 2,
              type: 'Flex',
              components: [3],
              styles: {
                justifyContent: 'center'
              }
            }],
            styles: {
              '.container': {
                display: 'flex',
                flexDirection: 'column',
                gap: '20px'
              },
              '.header': {
                fontSize: '24px',
                fontWeight: 'bold',
                marginBottom: '16px'
              }
            },
            status: 'online'
          },
          _meta: {
            source: 'mock-websocket',
            timestamp: new Date().toISOString(),
            requestId: data.id || data.timestamp
          }
        },
        timestamp: new Date().toISOString()
      };
      this._sendMessage(response);
    }

    // Handle generic message
  }, {
    key: "_handleGenericMessage",
    value: function _handleGenericMessage(data) {
      // Echo the message back
      var response = {
        type: 'echo',
        originalMessage: data,
        timestamp: new Date().toISOString(),
        server: 'MockWebSocketServer'
      };
      this._sendMessage(response);
    }

    // Send a message to the client
  }, {
    key: "_sendMessage",
    value: function _sendMessage(data) {
      var messageData = JSON.stringify(data);

      // Create message event
      var messageEvent = new MessageEvent('message', {
        data: messageData,
        origin: this.url,
        lastEventId: '',
        source: null,
        ports: []
      });

      // Dispatch message event
      this.dispatchEvent(messageEvent);
      if (typeof this.onmessage === 'function') {
        this.onmessage(messageEvent);
      }
      console.log("Mock WebSocket received data:", data);
    }
  }]);
}(/*#__PURE__*/(0,wrapNativeSuper/* default */.A)(EventTarget));
/**
 * Initialize the mock WebSocket server
 * @param {Object} options - Configuration options
 * @param {boolean} options.enabled - Whether the mock server is enabled
 * @param {boolean} options.logMessages - Whether to log messages
 */
(0,defineProperty/* default */.A)(MockWebSocket, "CONNECTING", 0);
(0,defineProperty/* default */.A)(MockWebSocket, "OPEN", 1);
(0,defineProperty/* default */.A)(MockWebSocket, "CLOSING", 2);
(0,defineProperty/* default */.A)(MockWebSocket, "CLOSED", 3);
function initMockWebSocketServer() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _options$enabled = options.enabled,
    enabled = _options$enabled === void 0 ? "production" === 'development' : _options$enabled,
    _options$logMessages = options.logMessages,
    logMessages = _options$logMessages === void 0 ? true : _options$logMessages;
  if (!enabled) {
    console.log('Mock WebSocket server is disabled');
    return;
  }
  console.log('Initializing mock WebSocket server...');

  // Store the original WebSocket class
  window._originalWebSocket = window.WebSocket;

  // Override the WebSocket class
  window.WebSocket = MockWebSocket;
  console.log('Mock WebSocket server initialized');
}

/**
 * Disable the mock WebSocket server
 */
function disableMockWebSocketServer() {
  // Restore the original WebSocket class if it exists
  if (window._originalWebSocket) {
    window.WebSocket = window._originalWebSocket;
    console.log('Mock WebSocket server disabled');
  }
}
/* harmony default export */ const mockWebSocketServer = ({
  initMockWebSocketServer: initMockWebSocketServer,
  disableMockWebSocketServer: disableMockWebSocketServer,
  MockWebSocket: MockWebSocket
});
;// ./src/index.js
var _window$WebSocket;












// Simple CSS for basic styling
var styles = "\n  body {\n    margin: 0;\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n      sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n\n  * {\n    box-sizing: border-box;\n  }\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n";

// Add styles to document
var styleSheet = document.createElement('style');
styleSheet.textContent = styles;
document.head.appendChild(styleSheet);

// Get root element
var rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error('Root element not found');
}

// Create React root
var root = (0,client/* createRoot */.H)(rootElement);

// Simple App component that bypasses complex initialization
var SimpleApp = function SimpleApp() {
  return /*#__PURE__*/react.createElement(react_redux/* Provider */.Kq, {
    store: store/* default */.A
  }, /*#__PURE__*/react.createElement(AuthContext/* AuthProvider */.OJ, null, /*#__PURE__*/react.createElement(EnhancedErrorBoundary/* default */.A, null, /*#__PURE__*/react.createElement(ThemeManager/* ThemeProvider */.NP, {
    initialTheme: "light"
  }, /*#__PURE__*/react.createElement(dist/* BrowserRouter */.Kd, null, /*#__PURE__*/react.createElement(Routes/* default */.A, null))))));
};

// FORCE DISABLE MOCK WEBSOCKET - Multiple aggressive checks
console.log('🔌 FORCING REAL WEBSOCKET MODE - Disabling all mock WebSocket functionality...');

// Check if mock WebSocket is active and disable it
if (window.WebSocket !== window._originalWebSocket && window._originalWebSocket) {
  console.log('🔌 Mock WebSocket detected - restoring real WebSocket...');
  disableMockWebSocketServer();
}

// Additional check - if WebSocket class has mock characteristics, restore native
if (window.WebSocket && window.WebSocket.name === 'MockWebSocket') {
  console.log('🔌 MockWebSocket class detected - forcing native WebSocket...');
  if (window._originalWebSocket) {
    window.WebSocket = window._originalWebSocket;
  }
}

// Set global flags to ensure real API usage
window.USE_REAL_API = true;
window.MOCK_SERVERS_ENABLED = false;
window.FORCE_REAL_WEBSOCKET = true;

// Log current WebSocket class for debugging
console.log('🔌 Current WebSocket class:', ((_window$WebSocket = window.WebSocket) === null || _window$WebSocket === void 0 ? void 0 : _window$WebSocket.name) || 'WebSocket');
console.log('🔌 Real API mode:', window.USE_REAL_API);
console.log('🔌 Mock servers disabled:', !window.MOCK_SERVERS_ENABLED);

// Render the app
try {
  console.log('🚀 Starting App Builder 201...');
  console.log('🔌 Using real WebSocket connections to backend');
  root.render(/*#__PURE__*/react.createElement(react.StrictMode, null, /*#__PURE__*/react.createElement(SimpleApp, null)));
  console.log('✅ App Builder 201 loaded successfully!');

  // Register service worker for offline support and caching
  (0,serviceWorkerRegistration/* register */.kz)({
    onSuccess: function onSuccess(registration) {
      console.log('✅ Service Worker registered successfully:', registration);
    },
    onUpdate: function onUpdate(registration) {
      console.log('🔄 Service Worker updated:', registration);
      // You could show a notification to the user here
    },
    onWaiting: function onWaiting() {
      console.log('⏳ Service Worker waiting for activation');
    }
  });
} catch (error) {
  console.error('❌ Failed to load App Builder 201:', error);

  // Fallback UI
  root.render(/*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      fontFamily: 'Arial, sans-serif',
      background: '#f8fafc',
      color: '#1f2937',
      textAlign: 'center',
      padding: '2rem'
    }
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      background: 'white',
      padding: '2rem',
      borderRadius: '12px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
      maxWidth: '500px'
    }
  }, /*#__PURE__*/react.createElement("h1", {
    style: {
      margin: '0 0 1rem',
      color: '#dc2626'
    }
  }, "App Loading Error"), /*#__PURE__*/react.createElement("p", {
    style: {
      margin: '0 0 1rem'
    }
  }, "There was an error loading the App Builder application."), /*#__PURE__*/react.createElement("button", {
    onClick: function onClick() {
      return window.location.reload();
    },
    style: {
      background: '#3b82f6',
      color: 'white',
      border: 'none',
      padding: '0.75rem 1.5rem',
      borderRadius: '6px',
      cursor: 'pointer',
      fontSize: '1rem'
    }
  }, "Reload Page"))));
}

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, [5124,6527,5108,9031,8980,6434,5101,1025,9156,276,720,5435,1114,7571,2518,9060,4772,7449,5141,9832,2698,2743,1629,8287,5000,1789,7192,9372,4802,1807,747,7088,8278,6059,8346,687,2036,4488,7496,9681,2773,6110,6037,3357,2272,895,1115,3205,1540,6261,9210,8104,9225,6438,6248,2378,8954,14,38,5588,1387,5955,7419,3307,7077], () => (__webpack_exec__(93590)));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
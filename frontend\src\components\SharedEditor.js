import React, { useState, useRef, useEffect } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { Card, Typography, Space, Tooltip, Badge, Button } from 'antd';
import { EyeOutlined, EditOutlined, SyncOutlined, TeamOutlined } from '@ant-design/icons';
import sharedEditingService from '../services/SharedEditingService';
import userPresenceService from '../services/UserPresenceService';
import UserPresenceIndicator from './UserPresenceIndicator';

const { Text } = Typography;

/**
 * SharedEditor component
 * Provides collaborative text editing functionality
 */
const SharedEditor = ({
  documentId,
  title = 'Shared Document',
  height = 300,
  readOnly = false,
  userId,
  username,
  onContentChange,
  style = {}
}) => {
  const [content, setContent] = useState('');
  const [collaborators, setCollaborators] = useState({});
  const [isJoined, setIsJoined] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const editorRef = useRef(null);
  const lastContentChangeTime = useRef(0);
  const operationsQueue = useRef([]);
  const lastCursorPosition = useRef(0);
  const lastSelectionRange = useRef({ start: 0, end: 0 });

  // Initialize services and join document
  useEffect(() => {
    if (!documentId || !userId) return;

    // Initialize services if needed
    if (!sharedEditingService.initialized) {
      sharedEditingService.init({ userId, username });
    }

    if (!userPresenceService.initialized) {
      userPresenceService.init({ userId, username });
    }

    // Join document
    setIsSyncing(true);
    const document = sharedEditingService.joinDocument(documentId);
    setIsJoined(true);

    // Set initial content if available
    if (document && document.content) {
      setContent(document.content);
    }

    // Set initial collaborators if available
    if (document && document.collaborators) {
      setCollaborators(document.collaborators);
    }

    setIsSyncing(false);

    // Clean up on unmount
    return () => {
      if (isJoined) {
        sharedEditingService.leaveDocument(documentId);
        setIsJoined(false);
      }
    };
  }, [documentId, userId, username]);

  // Listen for document updates
  useEffect(() => {
    if (!documentId) return;

    // Handle document update
    const handleDocumentUpdate = (data) => {
      if (data.documentId !== documentId) return;

      // Get document
      const document = sharedEditingService.getDocument(documentId);

      if (document) {
        // Update content
        setContent(document.content);

        // Call onContentChange callback if provided
        if (onContentChange) {
          onContentChange(document.content);
        }
      }
    };

    // Handle document sync
    const handleDocumentSync = (data) => {
      if (data.documentId !== documentId) return;

      setIsSyncing(false);

      // Get document
      const document = sharedEditingService.getDocument(documentId);

      if (document) {
        // Update content
        setContent(document.content);

        // Update collaborators
        if (document.collaborators) {
          setCollaborators(document.collaborators);
        }

        // Call onContentChange callback if provided
        if (onContentChange) {
          onContentChange(document.content);
        }
      }
    };

    // Handle document join
    const handleDocumentJoin = (data) => {
      if (data.documentId !== documentId) return;

      // Get document
      const document = sharedEditingService.getDocument(documentId);

      if (document && document.collaborators) {
        setCollaborators(document.collaborators);
      }
    };

    // Handle document leave
    const handleDocumentLeave = (data) => {
      if (data.documentId !== documentId) return;

      // Get document
      const document = sharedEditingService.getDocument(documentId);

      if (document && document.collaborators) {
        setCollaborators(document.collaborators);
      }
    };

    // Register event listeners
    sharedEditingService.on('document_update', handleDocumentUpdate);
    sharedEditingService.on('document_sync', handleDocumentSync);
    sharedEditingService.on('document_join', handleDocumentJoin);
    sharedEditingService.on('document_leave', handleDocumentLeave);

    // Clean up event listeners
    return () => {
      sharedEditingService.off('document_update', handleDocumentUpdate);
      sharedEditingService.off('document_sync', handleDocumentSync);
      sharedEditingService.off('document_join', handleDocumentJoin);
      sharedEditingService.off('document_leave', handleDocumentLeave);
    };
  }, [documentId, onContentChange]);

  // Handle content change
  const handleContentChange = (newContent) => {
    const oldContent = content;

    // Update local content
    setContent(newContent);

    // Call onContentChange callback if provided
    if (onContentChange) {
      onContentChange(newContent);
    }

    // Generate operations (would need to adapt for Quill delta format)
    const operations = generateOperations(oldContent, newContent);

    // Add operations to queue
    if (operations.length > 0) {
      operationsQueue.current = [...operationsQueue.current, ...operations];
    }

    // Throttle updates
    const now = Date.now();
    if (now - lastContentChangeTime.current > 500) {
      sendContentUpdate();
      lastContentChangeTime.current = now;
    } else {
      setTimeout(() => {
        sendContentUpdate();
      }, 500);
    }
  };

  // Send content update
  const sendContentUpdate = () => {
    if (operationsQueue.current.length === 0) return;

    // Send update
    sharedEditingService.updateContent(
      documentId,
      content,
      operationsQueue.current
    );

    // Clear operations queue
    operationsQueue.current = [];

    // Update last content change time
    lastContentChangeTime.current = Date.now();
  };

  // Generate operations from old and new content
  const generateOperations = (oldContent, newContent) => {
    // Simple diff algorithm - this could be improved with a proper diff library
    if (oldContent === newContent) return [];

    // For simplicity, just create a replace operation
    return [{
      type: 'replace',
      position: 0,
      length: oldContent.length,
      text: newContent
    }];
  };

  // Handle cursor position change
  const handleCursorPositionChange = (e) => {
    if (!editorRef.current) return;

    const cursorPosition = e.target.selectionStart;

    // Only send update if position changed
    if (cursorPosition !== lastCursorPosition.current) {
      lastCursorPosition.current = cursorPosition;

      // Send cursor update
      sharedEditingService.updateCursor(documentId, cursorPosition);
    }
  };

  // Handle selection change
  const handleSelectionChange = (e) => {
    if (!editorRef.current) return;

    const selectionStart = e.target.selectionStart;
    const selectionEnd = e.target.selectionEnd;

    // Only send update if selection changed
    if (selectionStart !== lastSelectionRange.current?.start ||
      selectionEnd !== lastSelectionRange.current?.end) {

      lastSelectionRange.current = {
        start: selectionStart,
        end: selectionEnd
      };

      // Only send if there's an actual selection
      if (selectionStart !== selectionEnd) {
        // Send selection update
        sharedEditingService.updateSelection(documentId, {
          start: selectionStart,
          end: selectionEnd
        });
      }
    }
  };

  // Request sync
  const requestSync = () => {
    setIsSyncing(true);

    // Send sync request
    sharedEditingService.sendDocumentMessage({
      subtype: 'document_sync_request',
      documentId
    });
  };

  // Get collaborator count
  const getCollaboratorCount = () => {
    return Object.keys(collaborators).length;
  };

  // Render collaborator list
  const renderCollaborators = () => {
    const collaboratorList = Object.values(collaborators);

    if (collaboratorList.length === 0) {
      return <Text type="secondary">No collaborators</Text>;
    }

    return (
      <Space wrap>
        {collaboratorList.map(collaborator => (
          <UserPresenceIndicator
            key={collaborator.userId}
            userId={collaborator.userId}
            username={collaborator.username}
            showUsername={false}
            size="small"
            avatarSize={24}
          />
        ))}
      </Space>
    );
  };

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <EditOutlined style={{ marginRight: 8 }} />
          <span>{title}</span>
          {isSyncing && <SyncOutlined spin style={{ marginLeft: 8 }} />}
        </div>
      }
      extra={
        <Space>
          <Tooltip title="Collaborators">
            <Badge count={getCollaboratorCount()} size="small">
              <Button
                icon={<TeamOutlined />}
                size="small"
                type="text"
              />
            </Badge>
          </Tooltip>
          <Tooltip title="Sync Document">
            <Button
              icon={<SyncOutlined spin={isSyncing} />}
              size="small"
              onClick={requestSync}
              disabled={isSyncing}
            />
          </Tooltip>
        </Space>
      }
      style={{ ...style }}
    >
      <div style={{ marginBottom: 8 }}>
        {renderCollaborators()}
      </div>

      <div data-testid="quill-editor">
        <ReactQuill
          ref={editorRef}
          value={content}
          onChange={handleContentChange}
          readOnly={readOnly || !isJoined}
          style={{ height }}
          theme="snow"
          data-testid="quill-content"
          modules={{
            toolbar: [
              ['bold', 'italic', 'underline', 'strike'],
              ['blockquote', 'code-block'],
              [{ 'header': 1 }, { 'header': 2 }],
              [{ 'list': 'ordered' }, { 'list': 'bullet' }],
              [{ 'script': 'sub' }, { 'script': 'super' }],
              [{ 'indent': '-1' }, { 'indent': '+1' }],
              [{ 'color': [] }, { 'background': [] }],
              ['clean']
            ]
          }}
        />
      </div>

      {readOnly && (
        <div style={{ marginTop: 8 }}>
          <Text type="secondary">
            <EyeOutlined /> Read-only mode
          </Text>
        </div>
      )}
    </Card>
  );
};

export default SharedEditor;


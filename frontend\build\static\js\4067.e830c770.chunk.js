"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4067],{

/***/ 94067:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(35346);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(11080);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(71468);
/* harmony import */ var _redux_actions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(81616);
/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(49391);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(79146);
/* harmony import */ var _design_system_theme__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(86020);




var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7;










var Title = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Text;

// Styled components
var LoginContainer = _design_system__WEBPACK_IMPORTED_MODULE_12__.styled.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  padding: ", ";\n  background-color: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_13__/* ["default"].spacing */ .Ay.spacing[4], _design_system_theme__WEBPACK_IMPORTED_MODULE_13__/* ["default"].colors */ .Ay.colors.neutral[100]);
var LoginCard = (0,_design_system__WEBPACK_IMPORTED_MODULE_12__.styled)(antd__WEBPACK_IMPORTED_MODULE_6__/* .Card */ .Zp)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  width: 100%;\n  max-width: 480px;\n  box-shadow: ", ";\n  border-radius: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_13__/* ["default"].shadows */ .Ay.shadows.lg, _design_system_theme__WEBPACK_IMPORTED_MODULE_13__/* ["default"].borderRadius */ .Ay.borderRadius.lg);
var LoginForm = (0,_design_system__WEBPACK_IMPORTED_MODULE_12__.styled)(antd__WEBPACK_IMPORTED_MODULE_6__/* .Form */ .lV)(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  .ant-form-item-label {\n    text-align: left;\n  }\n"])));
var LoginButton = (0,_design_system__WEBPACK_IMPORTED_MODULE_12__.styled)(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  width: 100%;\n"])));
var ForgotPasswordLink = (0,_design_system__WEBPACK_IMPORTED_MODULE_12__.styled)(react_router_dom__WEBPACK_IMPORTED_MODULE_8__/* .Link */ .N_)(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  float: right;\n"])));
var RegisterLink = (0,_design_system__WEBPACK_IMPORTED_MODULE_12__.styled)(Text)(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: block;\n  text-align: center;\n  margin-top: 16px;\n"])));
var SocialButton = (0,_design_system__WEBPACK_IMPORTED_MODULE_12__.styled)(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n)(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n"])));

/**
 * LoginPage component
 * Handles user authentication (login and registration)
 */
var LoginPage = function LoginPage() {
  var _location$state;
  var navigate = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_8__/* .useNavigate */ .Zp)();
  var location = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_8__/* .useLocation */ .zy)();
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_9__/* .useDispatch */ .wA)();
  var _useAuth = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_11__/* .useAuth */ .As)(),
    login = _useAuth.login,
    register = _useAuth.register,
    isAuthenticated = _useAuth.isAuthenticated,
    isLoading = _useAuth.isLoading,
    authError = _useAuth.error;
  var _Form$useForm = antd__WEBPACK_IMPORTED_MODULE_6__/* .Form */ .lV.useForm(),
    _Form$useForm2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    isLogin = _useState2[0],
    setIsLogin = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    error = _useState4[0],
    setError = _useState4[1];

  // Get redirect path from location state or default to '/dashboard'
  var from = ((_location$state = location.state) === null || _location$state === void 0 || (_location$state = _location$state.from) === null || _location$state === void 0 ? void 0 : _location$state.pathname) || '/dashboard';

  // Check if user is already logged in
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    if (isAuthenticated) {
      navigate(from, {
        replace: true
      });
    }
  }, [isAuthenticated, navigate, from]);

  // Update local error state when auth error changes
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    setError(authError);
  }, [authError]);

  // Handle form submission
  var handleSubmit = /*#__PURE__*/function () {
    var _ref = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee(values) {
      var result, userData, _result, _t;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            setError(null);
            _context.prev = 1;
            if (!isLogin) {
              _context.next = 3;
              break;
            }
            _context.next = 2;
            return login(values.email, values.password);
          case 2:
            result = _context.sent;
            if (result.success) {
              antd__WEBPACK_IMPORTED_MODULE_6__/* .message */ .iU.success('Login successful!');
              navigate(from, {
                replace: true
              });
            } else {
              setError(result.error || 'Login failed');
            }
            _context.next = 5;
            break;
          case 3:
            // Use real registration
            userData = {
              username: values.email.split('@')[0],
              // Use email prefix as username
              email: values.email,
              password: values.password,
              first_name: values.firstName,
              last_name: values.lastName
            };
            _context.next = 4;
            return register(userData);
          case 4:
            _result = _context.sent;
            if (_result.success) {
              antd__WEBPACK_IMPORTED_MODULE_6__/* .message */ .iU.success('Registration successful!');
              navigate(from, {
                replace: true
              });
            } else {
              setError(_result.error || 'Registration failed');
            }
          case 5:
            _context.next = 7;
            break;
          case 6:
            _context.prev = 6;
            _t = _context["catch"](1);
            console.error('Authentication error:', _t);
            setError(_t.message || 'An error occurred during authentication');
          case 7:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 6]]);
    }));
    return function handleSubmit(_x) {
      return _ref.apply(this, arguments);
    };
  }();

  // Toggle between login and registration forms
  var toggleAuthMode = function toggleAuthMode() {
    form.resetFields();
    setIsLogin(!isLogin);
    setError(null);
  };

  // Handle social login
  var handleSocialLogin = function handleSocialLogin(provider) {
    antd__WEBPACK_IMPORTED_MODULE_6__/* .message */ .iU.info("".concat(provider, " login is not implemented in this demo"));
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(LoginContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(LoginCard, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      textAlign: 'center',
      marginBottom: _design_system_theme__WEBPACK_IMPORTED_MODULE_13__/* ["default"].spacing */ .Ay.spacing[4]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Title, {
    level: 2,
    style: {
      margin: 0
    }
  }, isLogin ? 'Welcome Back' : 'Create Account'), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, {
    type: "secondary"
  }, isLogin ? 'Sign in to continue to App Builder' : 'Register to start building amazing applications')), error && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Alert */ .Fc, {
    message: "Authentication Error",
    description: error,
    type: "error",
    showIcon: true,
    style: {
      marginBottom: _design_system_theme__WEBPACK_IMPORTED_MODULE_13__/* ["default"].spacing */ .Ay.spacing[4]
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(LoginForm, {
    form: form,
    name: "auth_form",
    layout: "vertical",
    onFinish: handleSubmit,
    initialValues: {
      remember: true
    }
  }, !isLogin && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, {
    style: {
      display: 'flex',
      gap: _design_system_theme__WEBPACK_IMPORTED_MODULE_13__/* ["default"].spacing */ .Ay.spacing[3]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Form */ .lV.Item, {
    name: "firstName",
    label: "First Name",
    rules: [{
      required: true,
      message: 'Please enter your first name'
    }],
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Input */ .pd, {
    placeholder: "First Name"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Form */ .lV.Item, {
    name: "lastName",
    label: "Last Name",
    rules: [{
      required: true,
      message: 'Please enter your last name'
    }],
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Input */ .pd, {
    placeholder: "Last Name"
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Form */ .lV.Item, {
    name: "email",
    label: isLogin ? "Email or Username" : "Email",
    rules: [{
      required: true,
      message: 'Please enter your email'
    }].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(isLogin ? [] : [{
      type: 'email',
      message: 'Please enter a valid email'
    }]))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Input */ .pd, {
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .UserOutlined */ .qmv, null),
    placeholder: "Email"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Form */ .lV.Item, {
    name: "password",
    label: "Password",
    rules: [{
      required: true,
      message: 'Please enter your password'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Input */ .pd.Password, {
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .LockOutlined */ .sXv, null),
    placeholder: "Password"
  })), !isLogin && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Form */ .lV.Item, {
    name: "confirmPassword",
    label: "Confirm Password",
    dependencies: ['password'],
    rules: [{
      required: true,
      message: 'Please confirm your password'
    }, function (_ref2) {
      var getFieldValue = _ref2.getFieldValue;
      return {
        validator: function validator(_, value) {
          if (!value || getFieldValue('password') === value) {
            return Promise.resolve();
          }
          return Promise.reject(new Error('The two passwords do not match'));
        }
      };
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Input */ .pd.Password, {
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .LockOutlined */ .sXv, null),
    placeholder: "Confirm Password"
  })), isLogin && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Form */ .lV.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Form */ .lV.Item, {
    name: "remember",
    valuePropName: "checked",
    noStyle: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Checkbox */ .Sc, null, "Remember me")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ForgotPasswordLink, {
    to: "/forgot-password"
  }, "Forgot password?")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Form */ .lV.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(LoginButton, {
    type: "primary",
    htmlType: "submit",
    size: "large",
    loading: isLoading
  }, isLogin ? 'Sign In' : 'Create Account'))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Divider */ .cG, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, {
    type: "secondary"
  }, "Or continue with")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, {
    direction: "horizontal",
    style: {
      width: '100%',
      justifyContent: 'center',
      gap: _design_system_theme__WEBPACK_IMPORTED_MODULE_13__/* ["default"].spacing */ .Ay.spacing[3],
      marginBottom: _design_system_theme__WEBPACK_IMPORTED_MODULE_13__/* ["default"].spacing */ .Ay.spacing[4]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(SocialButton, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .GoogleOutlined */ .IhG, null),
    onClick: function onClick() {
      return handleSocialLogin('Google');
    }
  }, "Google"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(SocialButton, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .GithubOutlined */ .yGI, null),
    onClick: function onClick() {
      return handleSocialLogin('GitHub');
    }
  }, "GitHub")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      textAlign: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, {
    type: "secondary"
  }, isLogin ? "Don't have an account? " : "Already have an account? ", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
    type: "link",
    onClick: toggleAuthMode,
    style: {
      padding: 0
    }
  }, isLogin ? 'Sign up now' : 'Sign in')))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoginPage);

/***/ })

}]);